import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DialpadBottomButtons extends StatelessWidget {
  final GestureTapCallback? onLeftButtonClick,
      onMiddleButtonClick,
      onRightButtonClick,
      onLeftButtonLongPress,
      onMiddleButtonLongPress,
      onRightButtonLongPress;
  final IconData leftIcon, middleIcon, rightIcon;
  final Color? leftButtonColor, centerButtonColor, rightButtonColor;
  final Color? leftIconColor, centerIconColor, rightIconColor;

  const DialpadBottomButtons({
    required this.leftIcon,
    required this.middleIcon,
    required this.rightIcon,
    this.onLeftButtonClick,
    this.onLeftButtonLongPress,
    this.onMiddleButtonClick,
    this.onMiddleButtonLongPress,
    this.onRightButtonClick,
    this.onRightButtonLongPress,
    this.leftButtonColor,
    this.centerButtonColor,
    this.rightButtonColor,
    this.leftIconColor,
    this.centerIconColor,
    this.rightIconColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final defaultIconColor = colorTheme.roundShapeInkWellColor;
        final defaultButtonColor = colorTheme.primaryColor;

        // const size = heightLarge;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            RoundShapeInkWell(
              onTap: () {
                if (onLeftButtonClick != null) {
                  onLeftButtonClick!();
                }
              },
              onLongPress: () {
                if (onLeftButtonLongPress != null) {
                  onLeftButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                leftIcon,
                color: leftIconColor ?? defaultIconColor,
                size: iconSizeLarge,
              ),
              color: leftButtonColor ?? defaultButtonColor,
            ),
            RoundShapeInkWell(
              onTap: () {
                if (onMiddleButtonClick != null) {
                  onMiddleButtonClick!();
                }
              },
              onLongPress: () {
                if (onMiddleButtonLongPress != null) {
                  onMiddleButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                middleIcon,
                color: centerIconColor ?? defaultIconColor,
                size: iconSizeLarge,
              ),
              color: centerButtonColor ?? defaultButtonColor,
            ),
            RoundShapeInkWell(
              onTap: () {
                if (onRightButtonClick != null) {
                  onRightButtonClick!();
                }
              },
              onLongPress: () {
                if (onRightButtonLongPress != null) {
                  onRightButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                rightIcon,
                color: rightIconColor ?? defaultIconColor,
                size: iconSizeLarge,
              ),
              color: rightButtonColor ?? defaultButtonColor,
            ),
          ],
        );
      },
    );
  }
}
