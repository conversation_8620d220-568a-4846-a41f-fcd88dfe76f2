import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';

import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';

import 'package:ddone/services/voip_overlay_manager.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/constants/keys/widget_keys.dart';

class CallStateInfo {
  final IconData icon;
  final Color backgroundColor;
  final Color borderColor;
  final Color iconBackgroundColor;
  final Color iconColor;
  final Color textColor;
  final Color subtextColor;

  const CallStateInfo({
    required this.icon,
    required this.backgroundColor,
    required this.borderColor,
    required this.iconBackgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.subtextColor,
  });
}

class FloatingCallBubble extends StatefulWidget {
  final String? caller;
  final String? callerId;
  final String callState;
  final VoipSipEvent callEvent;
  final VoidCallback onExpand;

  const FloatingCallBubble({
    super.key,
    this.caller,
    this.callerId,
    required this.callState,
    required this.callEvent,
    required this.onExpand,
  });

  @override
  State<FloatingCallBubble> createState() => _FloatingCallBubbleState();
}

class _FloatingCallBubbleState extends State<FloatingCallBubble> {
  Offset _position = const Offset(20, 100);
  bool _isDragging = false;

  // Configuration for bubble dimensions and behavior
  final double bubbleWidth = 200.0;
  final double bubbleHeight = 80.0;

  // The default padding from the edge when snapping.
  final double snapPadding = 20.0;

  // The minimum part of the bubble that must remain visible.
  // This prevents the user from losing the bubble completely.
  final double minVisiblePart = 60.0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final textTheme = themeState.themeData.textTheme;
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;

        return Positioned(
          left: _position.dx,
          top: _position.dy,
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                _isDragging = true;
              });
            },
            onPanUpdate: (details) {
              final newPosition = _position + details.delta;
              setState(() {
                _position = Offset(
                  // Allow dragging between the defined off-screen limits.
                  newPosition.dx.clamp(
                    -bubbleWidth + minVisiblePart,
                    screenWidth - minVisiblePart,
                  ),
                  // Keep the bubble within the vertical screen bounds.
                  newPosition.dy.clamp(0.0, screenHeight - bubbleHeight),
                );
              });
            },
            onPanEnd: (details) {
              setState(() {
                _isDragging = false;
              });
              _snapToEdge();
            },
            onTap: () {
              if (!_isDragging) {
                widget.onExpand();
              }
            },
            child: Material(
              elevation: _isDragging ? 8.0 : 4.0,
              borderRadius: BorderRadius.circular(40),
              color: _getCallStateInfo(widget.callEvent).backgroundColor,
              child: Container(
                width: bubbleWidth,
                height: bubbleHeight,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(
                    color: _getCallStateInfo(widget.callEvent).borderColor,
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    // State Icon
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getCallStateInfo(widget.callEvent).iconBackgroundColor,
                      ),
                      child: Icon(
                        _getCallStateInfo(widget.callEvent).icon,
                        color: _getCallStateInfo(widget.callEvent).iconColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Call info
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.caller ?? widget.callerId ?? 'Unknown',
                            style: textTheme.bodyMedium!.copyWith(
                              color: _getCallStateInfo(widget.callEvent).textColor,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            widget.callState,
                            style: textTheme.bodySmall!.copyWith(
                              color: _getCallStateInfo(widget.callEvent).subtextColor,
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _snapToEdge() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Define the boundaries for the "middle" area of the screen.
    // If the bubble is released here, it will snap.
    final double leftSnapBoundary = snapPadding;
    final double rightSnapBoundary = screenWidth - bubbleWidth - snapPadding;

    // Check if the bubble was released in the middle.
    if (_position.dx > leftSnapBoundary && _position.dx < rightSnapBoundary) {
      // Determine which edge is closer based on the center of the screen.
      if ((_position.dx + bubbleWidth / 2) < screenWidth / 2) {
        // Snap to the default left position.
        setState(() {
          _position = Offset(snapPadding, _position.dy);
        });
      } else {
        // Snap to the default right position.
        setState(() {
          _position = Offset(rightSnapBoundary, _position.dy);
        });
      }
    }
    // If the bubble is NOT in the middle area, it means the user has
    // already placed it near an edge (either fully visible or partially hidden).
    // In this case, we do nothing and let it stay where the user dropped it.
  }

  CallStateInfo _getCallStateInfo(VoipSipEvent event) {
    switch (event) {
      case VoipSipEvent.incomingCall:
        return const CallStateInfo(
          icon: Icons.phone_callback,
          backgroundColor: Color(0xFF1A1A1A),
          borderColor: Color(0xFF4CAF50),
          iconBackgroundColor: Color(0xFF2E2E2E),
          iconColor: Color(0xFF4CAF50),
          textColor: Colors.white,
          subtextColor: Color(0xFF4CAF50),
        );
      case VoipSipEvent.calling:
      case VoipSipEvent.proceeding:
      case VoipSipEvent.ringing:
      case VoipSipEvent.loading:
        return const CallStateInfo(
          icon: Icons.phone_in_talk,
          backgroundColor: Color(0xFF1A1A1A),
          borderColor: Color(0xFF4CAF50),
          iconBackgroundColor: Color(0xFF2E2E2E),
          iconColor: Color(0xFF4CAF50),
          textColor: Colors.white,
          subtextColor: Color(0xFF4CAF50),
        );
      case VoipSipEvent.accepted:
      case VoipSipEvent.progress:
        return const CallStateInfo(
          icon: Icons.call,
          backgroundColor: Color(0xFF1A1A1A),
          borderColor: Color(0xFFFF9800),
          iconBackgroundColor: Color(0xFF2E2E2E),
          iconColor: Color(0xFFFF9800),
          textColor: Colors.white,
          subtextColor: Color(0xFFFF9800),
        );
      case VoipSipEvent.hangingup:
      case VoipSipEvent.hangup:
        return const CallStateInfo(
          icon: Icons.call_end,
          backgroundColor: Color(0xFF1A1A1A),
          borderColor: Color(0xFFF44336),
          iconBackgroundColor: Color(0xFF2E2E2E),
          iconColor: Color(0xFFF44336),
          textColor: Colors.white,
          subtextColor: Color(0xFFF44336),
        );
      default:
        return const CallStateInfo(
          icon: Icons.call,
          backgroundColor: Color(0xFF1A1A1A),
          borderColor: Color(0xFF4CAF50),
          iconBackgroundColor: Color(0xFF2E2E2E),
          iconColor: Color(0xFF4CAF50),
          textColor: Colors.white,
          subtextColor: Color(0xFF4CAF50),
        );
    }
  }
}

class FloatingCallBubbleOverlay extends StatelessWidget {
  final Widget child;

  const FloatingCallBubbleOverlay({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VoipCubit, VoipState>(
      builder: (context, voipState) {
        // Show bubble for all active call states
        // final shouldShow = voipState is VoipSipIncomingCall ||
        //     voipState is VoipSipCalling ||
        //     voipState is VoipSipRinging ||
        //     voipState is VoipSipProceeding ||
        //     voipState is VoipSipProgress ||
        //     voipState is VoipSipAccepted;

        return Stack(
          children: [
            child,
            // if (shouldShow) _buildFloatingBubble(context, voipState),
          ],
        );
      },
    );
  }

  Widget _buildFloatingBubble(BuildContext context, VoipState voipState) {
    final overlayManager = sl.get<VoipOverlayManager>();

    String caller = '';
    String callerId = '';
    String callState = '';
    VoipSipEvent callEvent = VoipSipEvent.calling;

    // if (voipState is VoipSipAccepted) {
    //   caller = voipState.caller ?? voipState.callee ?? '';
    //   callerId = voipState.callerId ?? voipState.calleeId ?? '';
    //   callState = VoipSipEvent.accepted.statusMessage();
    //   callEvent = VoipSipEvent.accepted;
    // } else if (voipState is VoipSipCalling) {
    //   caller = voipState.callee ?? '';
    //   callerId = voipState.calleeId ?? '';
    //   callState = VoipSipEvent.calling.statusMessage();
    //   callEvent = VoipSipEvent.calling;
    // } else if (voipState is VoipSipRinging) {
    //   caller = voipState.callee ?? '';
    //   callerId = voipState.calleeId ?? '';
    //   callState = 'Ringing...';
    //   callEvent = VoipSipEvent.ringing;
    // } else if (voipState is VoipSipProceeding) {
    //   caller = voipState.callee ?? '';
    //   callerId = voipState.calleeId ?? '';
    //   callState = VoipSipEvent.proceeding.statusMessage();
    //   callEvent = VoipSipEvent.proceeding;
    // } else if (voipState is VoipSipProgress) {
    //   caller = voipState.callee ?? '';
    //   callerId = voipState.calleeId ?? '';
    //   callState = VoipSipEvent.progress.statusMessage();
    //   callEvent = VoipSipEvent.progress;
    // } else if (voipState is VoipSipIncomingCall) {
    //   caller = voipState.caller ?? '';
    //   callerId = voipState.callerId ?? '';
    //   callState = 'Incoming Call';
    //   callEvent = VoipSipEvent.incomingCall;
    // }

    return FloatingCallBubble(
      caller: caller,
      callerId: callerId,
      callState: callState,
      callEvent: callEvent,
      onExpand: () {
        // Use the global navigator key to show dialog from correct context
        final navigatorContext = WidgetKeys.navKey.currentContext;
        if (navigatorContext != null) {
          // Use forceShowCallDialog to show dialog even if previously minimized
          // overlayManager.forceShowCallDialog(navigatorContext, voipState);
        }
      },
    );
  }
}
