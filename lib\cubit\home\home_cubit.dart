import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/dialpad_enum.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/audio_session_service/audio_session_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/media_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/utils/permission_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:janus_client/janus_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> with ApiHandler, PrefsAware {
  final StopWatchTimer stopWatchTimer;
  final HiveService hiveService;
  final VoipService _voipService;
  final MediaService mediaService;
  final AudioSessionService audioSessionService;
  final AudioDeviceService _audioDeviceService;

  late PageController pageController;

  bool _hasMediaDevices = true;
  bool get hasMediaDevices => _hasMediaDevices;

  HomeCubit._({
    HomeState? state,
  })  : stopWatchTimer = sl.get<StopWatchTimer>(),
        hiveService = sl.get<HiveService>(),
        _voipService = sl.get<VoipService>(),
        mediaService = sl.get<MediaService>(),
        audioSessionService = sl.get<AudioSessionService>(),
        _audioDeviceService = AudioDeviceService(),
        super(state ?? const HomeInitial()) {
    pageController = PageController(
      initialPage: getPageViewIndex(
          homePageViewDefinition, isDesktop ? PageViewNameEnum.dialScreenWindows : PageViewNameEnum.dailpad),
      keepPage: true,
    );
    _checkMediaDevices();
  }

  Future<void> _checkMediaDevices() async {
    try {
      _hasMediaDevices = await _audioDeviceService.hasInputDevicesAvailable();

      if (!_hasMediaDevices) {
        log.w('No input devices (microphones) found during app initialization');
        EasyLoadingService().showErrorWithText(
          'No microphone detected. Calling functionality will be disabled.',
          duration: const Duration(seconds: 5),
        );
      } else {
        log.i('Input devices (microphones) available for calling');
      }
    } catch (e) {
      log.e('Failed to check input devices during initialization', error: e);
      _hasMediaDevices = false;
      EasyLoadingService().showErrorWithText(
        'Unable to detect microphone. Calling functionality may not work properly.',
        duration: const Duration(seconds: 5),
      );
    }
  }

  factory HomeCubit.initial({HomeState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<HomeCubit>()) {
      return sl.get<HomeCubit>();
    }
    return HomeCubit._(state: state);
  }

  void bottomTapped(int index) {
    updateSelectedIndex(index);
    pageController.jumpToPage(index);
  }

  void jumpToPage(int index) {
    pageController.jumpToPage(index);
  }

  void jumpToPageWithName(PageViewNameEnum name) {
    int index = getPageViewIndex(homePageViewDefinition, name);
    jumpToPage(index);
  }

  Future<void> initRtcClient({
    required AuthCubit authCubit,
    required VoipCubit voipCubit,
  }) async {
    final String? sipName;
    final String? sipProxy;
    final String? sipSecret;
    final String? sipNumber;
    final String? sipDomain;
    final String? sipWsUrl;

    final authState = authCubit.state;

    if (authState is AuthRegistering || authState is AuthManualRegistering) {
      authState as AuthRegistering;
      sipName = authState.sipName;
      sipProxy = authState.sipProxy;
      sipSecret = authState.sipSecret;
      sipNumber = authState.sipNumber;
      sipDomain = authState.sipDomain;
      sipWsUrl = authState.sipWsUrl;
    } else {
      sipName = prefs.getString(CacheKeys.sipName);
      sipProxy = prefs.getString(CacheKeys.sipProxy);
      sipSecret = prefs.getString(CacheKeys.sipPwd);
      sipNumber = prefs.getString(CacheKeys.sipNumber);
      sipDomain = prefs.getString(CacheKeys.sipDomain);
      sipWsUrl = prefs.getString(CacheKeys.sipWsUrl);
    }

    bool isNotSignedInOrRegistering =
        authState is AuthSuccess || authState is AuthRegistering || authState is AuthManualRegistering;

    if (!isNotSignedInOrRegistering) {
      return;
    }
    if (voipCubit.state is VoipConnected) {
      return;
    }

    await voipCubit.init(
      sipWsUrl: sipWsUrl,
      sipNumber: sipNumber,
      sipDomain: sipDomain,
      sipProxy: sipProxy,
      sipSecret: sipSecret,
      sipName: sipName,
    );
  }

  void startStopWatch() {
    stopWatchTimer.onResetTimer();
    int? callkitStartCallTime = prefs.getInt(CacheKeys.callkitStartCallTime);
    if (callkitStartCallTime != null) {
      int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
      int callDuration = currentTimestamp - callkitStartCallTime;
      stopWatchTimer.setPresetTime(mSec: callDuration, add: false);
    }
    stopWatchTimer.onStartTimer();
  }

  void updateNavBar(int index) {
    emit(
      HomeLoaded(
        navBarNotifier: index,
        navSelectedIndex: state.navSelectedIndex,
        isMicOn: state.isMicOn,
        prefilledDailpad: state.prefilledDailpad,
      ),
    );
  }

  void updateSelectedIndex(int index) {
    emit(
      HomeLoaded(
        navBarNotifier: state.navBarNotifier,
        navSelectedIndex: index,
        isMicOn: state.isMicOn,
        prefilledDailpad: state.prefilledDailpad,
      ),
    );
  }

  Future<void> playDialpadSound(DialpadEnum dialpadEnum) async {
    await mediaService.playDialpadTone(dialpadEnum);
  }

  // Future<void> declineCall() async {
  //   await mediaService.stopRingtone();
  //   await _voipService._janusService.waitJanusConnect();
  //   await _voipService.declineCall();
  // }

  // Future<void> acceptCall() async {
  //   await mediaService.stopRingtone();
  //   // Check if input devices (microphones) are available before accept the call
  //   bool hasInputDevices = await _audioDeviceService.hasInputDevicesAvailable();
  //   if (!hasInputDevices) {
  //     EasyLoadingService().showErrorWithText('No microphone detected.');
  //     return;
  //   }
  //   bool waitResult =
  //       await waitForCondition(() async => !audioSessionService.interrupted, timeout: _getAudioInterruptTimeout());
  //   if (!waitResult) log.w('audio session is still in interrupted mode, audio may mess up');
  //   await _voipService._janusService.waitJanusConnect();
  //   await _voipService.acceptCall();
  // }

  // Duration _getAudioInterruptTimeout() {
  //   if (isAndroid) {
  //     return const Duration(milliseconds: 1500);
  //   } else {
  //     return const Duration(milliseconds: 3000);
  //   }
  // }

  // Future<void> reconnectCall() async {
  //   await _voipService.acceptCall();
  // }

  // void transferCall({required String extNum}) async {
  //   final String? sipProxy = prefs.getString(CacheKeys.sipProxy);
  //   _voipService.transferCall(extNum, sipProxy!);
  // }

  // Future<void> hold() => _voipService.holdCall();

  // Future<void> unhold() => _voipService.unholdCall();

  // Future<void> toggleHold() => _voipService.toggleHoldCall();

  // Future<void> hangup() async {
  //   await _voipService.hangupCall();
  //   emit(
  //     HomeLoaded(
  //       navBarNotifier: state.navBarNotifier,
  //       navSelectedIndex: state.navSelectedIndex,
  //       isMicOn: true,
  //       prefilledDailpad: state.prefilledDailpad,
  //     ),
  //   );
  // }

  // void toggleMic() {
  //   emit(
  //     HomeLoaded(
  //       navBarNotifier: state.navBarNotifier,
  //       navSelectedIndex: state.navSelectedIndex,
  //       isMicOn: !state.isMicOn,
  //       prefilledDailpad: state.prefilledDailpad,
  //     ),
  //   );
  //   _voipService.muteMicAudio(!state.isMicOn);
  // }

  // Future<void> makeCall({
  //   String? receiverName,
  //   required String extNum,
  //   required ContactsCubit contactsCubit,
  //   required BuildContext context,
  // }) async {
  //   final String? sipProxy = prefs.getString(CacheKeys.sipProxy);

  //   if (_voipService.lastVoipState is! VoipSipRegistered) {
  //     EasyLoadingService().showErrorWithText('Please register account first');
  //     return;
  //   }

  //   // Check if input devices (microphones) are available before making the call
  //   bool hasInputDevices = await _audioDeviceService.hasInputDevicesAvailable();
  //   if (!hasInputDevices) {
  //     EasyLoadingService().showErrorWithText('No device found for media');
  //     return;
  //   }

  //   String? ctcName = receiverName;

  //   ctcName ??= getContactName(
  //     extNum: extNum,
  //     contactsCubit: contactsCubit,
  //   );

  //   if (isMobile && context.mounted) {
  //     bool hasMicPermission = await PermissionUtil.handlePermissionRequest(Permission.microphone, context);
  //     if (!hasMicPermission) return;

  //     if (isAndroid && context.mounted) {
  //       bool hasPhonePermission = await PermissionUtil.handlePermissionRequest(Permission.phone, context);
  //       if (!hasPhonePermission) return;
  //     }
  //   }

  //   _voipService.makeCall(extNum, sipProxy!, ctcName);
  // }

  // String getContactName({
  //   required ContactsCubit contactsCubit,
  //   required String extNum,
  // }) {
  //   String contactName = extNum;

  //   for (var contactModel in contactsCubit.state.contactModelList) {
  //     if (extNum == contactModel.contactId) {
  //       contactName = contactModel.displayName;
  //     }
  //   }

  //   return contactName;
  // }

  // void sendDTMF(String tone) async {
  //   _voipService.sendDTMF(tone);
  // }

  void checkForMissCall() async {
    final notificationData = prefs.getString(CacheKeys.missCall);

    if (notificationData != null) {
      await prefs.remove(CacheKeys.missCall);

      final caller = prefs.getString(CacheKeys.missCallName);
      final callerId = prefs.getString(CacheKeys.missCallId);
      final callTime = prefs.getString(CacheKeys.missCallTime);

      hiveService.addData<CallRecords>(
        data: CallRecords(
          contactName: '$caller',
          did: '$callerId',
          duration: '0:00',
          type: CallType.missed,
          datetime: DateTime.parse(callTime!),
        ),
      );
    }
  }

  void initiateCallFromChat(String extNum) async {
    if (isMobile) {
      popUntilInitial();
      jumpToPageWithName(PageViewNameEnum.dailpad);
    } else {
      jumpToPageWithName(PageViewNameEnum.dialScreenWindows);
    }
    emit(
      HomeLoaded(
        navBarNotifier: state.navBarNotifier,
        navSelectedIndex: state.navSelectedIndex,
        isMicOn: state.isMicOn,
        prefilledDailpad: extNum,
      ),
    );
  }

  void clearPrefilledDailpad() {
    emit(
      HomeLoaded(
        navBarNotifier: state.navBarNotifier,
        navSelectedIndex: state.navSelectedIndex,
        isMicOn: state.isMicOn,
        prefilledDailpad: null,
      ),
    );
  }

  @override
  Future<void> close() {
    // _voipService._janusService
    //     .removeJanusMessageListener(kHomeCubitJanusEventPriority, _onJanusEvent, onError: _onJanusError);
    mediaService.dispose();
    return super.close();
  }
}
