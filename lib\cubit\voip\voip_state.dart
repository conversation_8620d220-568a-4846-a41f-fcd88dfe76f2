part of 'voip_cubit.dart';

abstract class VoipState extends Equatable {
  const VoipState();

  Map<String, dynamic> toJson();

  static VoipState fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VoipInitial':
        return const VoipInitial();
      case 'VoipConnecting':
        return const VoipConnecting();
      case 'VoipConnected':
        return const VoipConnected();
      case 'VoipError':
        return VoipError(json['message'] ?? '');
      case 'VoipRunning':
        return VoipRunning(
          activeCalls: (json['activeCalls'] as List<dynamic>? ?? [])
              .map((e) => SipCallState.fromJson(e as Map<String, dynamic>))
              .toList(),
        );
      default:
        throw Exception("Unknown VoipState type: ${json['type']}");
    }
  }

  @override
  List<Object?> get props => [];
}

class VoipInitial extends VoipState {
  const VoipInitial();

  @override
  Map<String, dynamic> toJson() => {'type': 'VoipInitial'};
}

class VoipConnecting extends VoipState {
  const VoipConnecting();

  @override
  Map<String, dynamic> toJson() => {'type': 'VoipConnecting'};
}

class VoipConnected extends VoipState {
  const VoipConnected();

  @override
  Map<String, dynamic> toJson() => {'type': 'VoipConnected'};
}

class VoipError extends VoipState {
  final String message;
  const VoipError(this.message);

  @override
  List<Object?> get props => [message];

  @override
  Map<String, dynamic> toJson() => {
        'type': 'VoipError',
        'message': message,
      };
}

class VoipRunning extends VoipConnected {
  final List<SipCallState> activeCalls;
  const VoipRunning({this.activeCalls = const []});

  @override
  List<Object?> get props => [activeCalls];

  @override
  Map<String, dynamic> toJson() => {
        'type': 'VoipRunning',
        'activeCalls': activeCalls.map((c) => c.toJson()).toList(),
      };
}
