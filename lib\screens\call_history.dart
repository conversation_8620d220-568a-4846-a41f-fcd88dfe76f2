import 'package:ddone/components/list_tile/card_list_tile_with_title.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/call_history/call_history_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/utils/extensions/datetime_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

//direct-chat
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/utils/contact_finder_util.dart';

class CallHistory extends StatefulWidget {
  static const routeName = '/callHistory';

  const CallHistory({super.key});

  @override
  State<CallHistory> createState() => _CallHistoryState();
}

class _CallHistoryState extends State<CallHistory> {
  double _mouseX = 0.0;
  double _mouseY = 0.0;

  late HomeCubit _homeCubit;
  late ContactsCubit _contactsCubit;
  late CallHistoryCubit _callHistoryCubit;
  late InfoCubit _infoCubit;
  late MamListCubit _mamListCubit;
  late LoginCubit _loginCubit;
  late ChatRecentCubit _chatRecentCubit;
  late HiveService _hiveService;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
    _hiveService = sl.get<HiveService>();
    _callHistoryCubit = CallHistoryCubit.initial();

    _callHistoryCubit.getCallRecords();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: colorTheme.backgroundColor,
            centerTitle: true,
            title: const Text('Call History'),
          ),
          body: CallHistoryList(
            callHistoryCubit: _callHistoryCubit,
            homeCubit: _homeCubit,
            contactsCubit: _contactsCubit,
            infoCubit: _infoCubit,
            loginCubit: _loginCubit,
            mamListCubit: _mamListCubit,
            chatRecentCubit: _chatRecentCubit,
            hiveService: _hiveService,
            onPointerDown: (PointerDownEvent event) {
              _mouseX = event.position.dx;
              _mouseY = event.position.dy;

              setState(() {});
            },
            relativeRect: RelativeRect.fromLTRB(
              _mouseX,
              _mouseY,
              _mouseX + 100,
              _mouseY + 100,
            ),
          ),
        );
      },
    );
  }
}

class CallHistoryList extends StatelessWidget {
  final CallHistoryCubit callHistoryCubit;
  final HomeCubit homeCubit;
  final ContactsCubit contactsCubit;
  final InfoCubit infoCubit;
  final LoginCubit loginCubit;
  final MamListCubit mamListCubit;
  final ChatRecentCubit chatRecentCubit;
  final HiveService hiveService;
  final Function(PointerDownEvent) onPointerDown;
  final RelativeRect relativeRect;

  const CallHistoryList({
    required this.callHistoryCubit,
    required this.homeCubit,
    required this.contactsCubit,
    required this.infoCubit,
    required this.loginCubit,
    required this.mamListCubit,
    required this.chatRecentCubit,
    required this.hiveService,
    required this.onPointerDown,
    required this.relativeRect,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return BlocBuilder<CallHistoryCubit, CallHistoryState>(
          bloc: callHistoryCubit,
          builder: (context, callHistoryState) {
            final callHistoryList = callHistoryState.callRecordList;

            return ScrollbarTheme(
              data: ScrollbarThemeData(thumbColor: WidgetStatePropertyAll(Colors.grey.shade700)),
              child: callHistoryList.isEmpty
                  ? Center(
                      child: Text(
                        'No call history',
                        style: textTheme.titleMedium!.copyWith(
                          color: colorTheme.onPrimaryColor,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: callHistoryList.length,
                      itemBuilder: (context, index) {
                        final callRecord = callHistoryList[index];

                        bool showDate = true;

                        if (index != 0) {
                          showDate = callRecord.datetime.areNotSameDate(callHistoryList[index - 1].datetime);
                        }

                        final contactList = contactsCubit.state.contactModelList;

                        // Use call history specific method to handle data inconsistencies between call types
                        final contactInfo = ContactFinderUtil.findContactInfoForCallHistory(
                          did: callRecord.did,
                          contactName: callRecord.contactName,
                          contactList: contactList,
                        );

                        final sipAddress = contactInfo.sipAddress;
                        final contactNameToShow = contactInfo.displayName;

                        final isNotInContactList = !contactInfo.isFound;

                        return Center(
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
                            child: CallHistoryTile(
                              callRecords: callRecord,
                              contactNameToShow: isNotInContactList ? callRecord.did : contactNameToShow,
                              onPointerDown: onPointerDown,
                              onTap: () => showPopUpMenu(
                                context: context,
                                position: relativeRect,
                                color: colorTheme.onPrimaryColor,
                                items: <PopupMenuItem>[
                                  if (isNotInContactList)
                                    PopupMenuItem(
                                      child: Text(
                                        'Add to contact',
                                        style: textTheme.labelLarge!.copyWith(color: colorTheme.backgroundColor),
                                      ),
                                      onTap: () => showAddContactDialog(
                                        context,
                                        contactNumber: callRecord.contactName,
                                      ),
                                    ),
                                  PopupMenuItem(
                                    onTap: () {
                                      // homeCubit.makeCall(
                                      //   receiverName: contactInfo.displayName,
                                      //   extNum: contactInfo.callExtension,
                                      //   contactsCubit: contactsCubit,
                                      //   context: context,
                                      // );
                                    },
                                    child: Text(
                                      'Call',
                                      style: textTheme.labelLarge!.copyWith(color: colorTheme.backgroundColor),
                                    ),
                                  ),
                                  if (contactInfo.canMessage)
                                    PopupMenuItem(
                                      onTap: () async {
                                        popUntilInitial();
                                        homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);
                                        chatRecentCubit.setPageIndex(kDirectMessagesIndex);
                                        infoCubit.getChatHistory(
                                          receiver: sipAddress!,
                                          loginCubit: loginCubit,
                                          mamListCubit: mamListCubit,
                                        );
                                        if (isMobile) {
                                          Navigator.of(context).pushNamed(ChatPage.routeName);
                                        }
                                      },
                                      child: Text(
                                        'Message',
                                        style: textTheme.labelLarge!.copyWith(color: colorTheme.backgroundColor),
                                      ),
                                    ),
                                ],
                              ),
                              showDate: showDate,
                              isLastIndex: index == callHistoryList.length - 1,
                            ),
                          ),
                        );
                      },
                    ),
            );
          },
        );
      },
    );
  }
}

class CallHistoryTile extends StatelessWidget {
  final CallRecords callRecords;
  final String contactNameToShow;
  final Function(PointerDownEvent) onPointerDown;
  final VoidCallback onTap;
  final bool showDate, isLastIndex;

  const CallHistoryTile({
    required this.callRecords,
    required this.contactNameToShow,
    required this.onPointerDown,
    required this.onTap,
    required this.showDate,
    required this.isLastIndex,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        final dateTime = callRecords.datetime.isYesterday()
            ? 'Yesterday'
            : callRecords.datetime.isToday()
                ? 'Today'
                : callRecords.datetime.EEEEddMMMyyyyString();

        return Listener(
          onPointerDown: onPointerDown,
          child: CardListTileWithTitle(
            title: dateTime,
            listTileTitle: contactNameToShow,
            leadingIcon: Icon(
              callRecords.type.callTypeIcon(),
              color: callRecords.type.callTypeIconColor(colorTheme),
            ),
            showTitle: showDate,
            onTap: onTap,
            trailingWidget: Text(
              DateFormat.Hm().format(callRecords.datetime).toString(),
              style: textTheme.titleMedium!.copyWith(color: colorTheme.onPrimaryColor),
            ),
          ),
        );
      },
    );
  }
}
