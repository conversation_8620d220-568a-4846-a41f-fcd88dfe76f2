import 'dart:io';

import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/file_picker/file_picker_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';

class FilePickerDialog extends StatefulWidget {
  const FilePickerDialog({super.key});

  @override
  State<FilePickerDialog> createState() => _FilePickerDialogState();
}

class _FilePickerDialogState extends State<FilePickerDialog> {
  late LoginCubit _loginCubit;
  late MessagesCubit _messagesCubit;
  late MamListCubit _mamListCubit;
  late ChatUiCubit _chatUiCubit;
  late FilePickerCubit _filePickerCubit;
  late ContactsCubit _contactsCubit;

  @override
  void initState() {
    super.initState();
    _filePickerCubit = FilePickerCubit.initial();
    // Automatically start file picking when the dialog is opened
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _messagesCubit = BlocProvider.of<MessagesCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _chatUiCubit = BlocProvider.of<ChatUiCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _filePickerCubit.pickFile();
  }

  // Future<UploadResponse> _uploadFile(String putUrl, PlatformFile file) async {
  //   try {
  //     // Log the URL and file details for debugging
  //     print('Uploading file to: $putUrl');
  //     print('File name: ${file.name}');
  //     print('File size: ${file.size}');
  //     print('File bytes: ${file.bytes}');

  //     // Prepare the request
  //     var request = http.Request('PUT', Uri.parse(putUrl))
  //       ..headers['Content-Type'] = file.xFile.mimeType ?? 'application/octet-stream'
  //       ..bodyBytes = _formatBytes(file.size, 2) as List<int>;

  //     // Send the request
  //     var response = await request.send();

  //     // Log the response details
  //     print('Response status code: ${response.statusCode}');
  //     print('Response headers: ${response.headers}');

  //     if (response.statusCode == 200 || response.statusCode == 201) {
  //       return UploadResponse(isSuccess: true);
  //     } else {
  //       return UploadResponse(isSuccess: false, error: 'Failed to upload file: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     return UploadResponse(isSuccess: false, error: 'Exception occurred: $e');
  //   }
  // }

  String _formatBytes(int bytes, int decimals) {
    if (bytes <= 0) return '0 B';
    const k = 1024;
    final dm = decimals <= 0 ? 0 : decimals;
    const sizes = [
      'B',
      'KB',
      'MB',
    ];
    int i = 0;
    double fileSize = bytes.toDouble();
    while (fileSize >= k && i < sizes.length - 1) {
      fileSize /= k;
      i++;
    }
    final formattedSize = fileSize.toStringAsFixed(dm);
    if (formattedSize.contains('.')) {
      final split = formattedSize.split('.');
      var wholePart = int.parse(split[0]);
      final decimalPart = int.parse(split[1]);
      if (decimalPart >= 5) {
        wholePart++; // Round up the whole part
        return '$wholePart ${sizes[i]}';
      }
    }
    return '$formattedSize ${sizes[i]}';
  }

  String _getFileTypeIcon(String? extension) {
    switch (extension?.toLowerCase()) {
      case 'doc':
      case 'docx':
        return '$iconPathPrefix/docx.png'; // Custom Word icon
      case 'pdf':
        return '$iconPathPrefix/pdf.png'; //Custom PDF icon
      case 'ppt':
      case 'pptx':
        return '$iconPathPrefix/ppt.png'; // Custom PowerPoint icon
      case 'xls':
      case 'xlsx':
        return '$iconPathPrefix/xlsx.png'; // Custom Excel icon
      case 'py':
        return '$iconPathPrefix/python.png'; // Custom python icon
      case 'html':
      case 'xml':
      case 'css':
        return '$iconPathPrefix/xml.png'; // Custom code icon
      case 'js':
        return '$iconPathPrefix/js.png'; // Custom js icon
      case 'json':
        return '$iconPathPrefix/json.png'; // Custom json icon
      case 'txt':
        return '$iconPathPrefix/text.png'; // Custom text icon
      case 'ini':
        return '$iconPathPrefix/ini.png'; // Custom ini icon
      // case 'mp4':
      //   return '$iconPathPrefix/video.png'; // Custom video icon
      // case 'mp3':
      //   return '$iconPathPrefix/music.png'; // Custom music icon
      default:
        return '$iconPathPrefix/genericfile.png'; // Default file icon
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FilePickerCubit, FilePickerState>(
      bloc: _filePickerCubit,
      builder: (context, filePickerState) {
        if (filePickerState is FilePickerSelected) {
          final fileResult = filePickerState.selectedFile;
          final filePath = fileResult.files.first;
          bool isImageFile = filePath.extension != null &&
              ['jpg', 'jpeg', 'png', 'gif', 'bmp'].contains(filePath.extension!.toLowerCase());

          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // if (_fileResult != null)
              //   Column(
              //     children: [
              //       Text('Selected file: ${_fileResult!.files.single.name}'),
              //       SizedBox(height: 16.0),
              //     ],
              //   ),
              BlocBuilder<FilePickerCubit, FilePickerState>(
                bloc: _filePickerCubit,
                builder: (context, state) {
                  return Container(
                    child: isImageFile
                        ? Expanded(
                            child: Image.file(
                              File(filePath.path.toString()),
                              fit: BoxFit.contain,
                            ),
                          )
                        : Image.asset(
                            _getFileTypeIcon(fileResult.files.first.extension),
                            height: 100,
                            width: 100,
                            color:
                                fileResult.files.first.extension == 'ini' || fileResult.files.first.extension == 'mp4'
                                    ? Colors.white60
                                    : null,
                          ),
                  );
                },
              ),
              Center(
                child: !isImageFile
                    ? Text(
                        fileResult.files.single.name,
                        style: const TextStyle(
                          color: Colors.white60,
                          fontSize: 16,
                        ),
                      )
                    : const Text(
                        '',
                      ),
              ),
              Center(
                child: !isImageFile
                    ? Text(
                        ' ${_formatBytes(fileResult.files.single.size, 2)}',
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      )
                    : const Text(
                        '',
                      ),
              ),
              !isImageFile
                  ? const SizedBox(
                      height: 16.0,
                    )
                  : const SizedBox(
                      height: 0.0,
                    ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    style: ButtonStyle(backgroundColor: WidgetStateProperty.resolveWith(
                      (Set<WidgetState> states) {
                        // if (states.contains(MaterialState.pressed)) {
                        //   return context.colorTheme().primaryContainer;
                        // }
                        return context.colorTheme().surface;
                      },
                    )),
                    onPressed: pop,
                    child: Text(
                      'Close',
                      style: context.textStyle().bodyMedium!.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Colors.white60,
                          ),
                    ),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  BlocBuilder<InfoCubit, InfoState>(
                    builder: (context, infoState) {
                      return BlocBuilder<FilePickerCubit, FilePickerState>(
                        bloc: _filePickerCubit,
                        builder: (context, filePickerState) {
                          final isUploading = filePickerState is FilePickerUploading;
                          return ElevatedButton(
                            style: ButtonStyle(backgroundColor: WidgetStateProperty.resolveWith(
                              (Set<WidgetState> states) {
                                // if (states.contains(MaterialState.pressed)) {
                                //   return Colors.amber[200];
                                // }
                                return context.colorTheme().surface;
                              },
                            )),
                            onPressed: isUploading
                                ? null
                                : () {
                                    _filePickerCubit.reqSlot(
                                      _loginCubit,
                                      _messagesCubit,
                                      _mamListCubit,
                                      _chatUiCubit,
                                      _contactsCubit,
                                      infoState.receiver.toString(),
                                    );
                                    pop();
                                  },
                            child: Text(
                              isUploading ? 'Uploading...' : 'Send',
                              style: context.textStyle().bodyMedium!.copyWith(
                                    color: isUploading ? Colors.white38 : Colors.white60,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ],
          );
        } else {
          return const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16.0),
              Text(
                'Loading file picker...',
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: 16,
                ),
              ),
              SizedBox(height: 16.0),
            ],
          );
        }
      },
    );

    // FutureBuilder<FilePickerResult?>(
    //   future: FilePicker.platform.pickFiles(),
    //   builder: (BuildContext context, AsyncSnapshot<FilePickerResult?> snapshot) {
    //     if (snapshot.connectionState == ConnectionState.done) {
    //       if (snapshot.hasError) {
    //         return Column(
    //           mainAxisSize: MainAxisSize.min,
    //           children: <Widget>[
    //             Text('Error: ${snapshot.error}'),
    //             const SizedBox(height: 16.0),
    //             ElevatedButton(
    //               onPressed: () => Navigator.of(context).pop(),
    //               child: const Text('Close'),
    //             ),
    //           ],
    //         );
    //       } else if (snapshot.hasData) {
    //         Navigator.of(context).pop(snapshot.data);
    //         return Container();
    //       } else {
    //         Navigator.of(context).pop(); // Handle the case where no file was picked
    //         return Container();
    //       }
    //     }

    //     return const Column(
    //       mainAxisSize: MainAxisSize.min,
    //       children: <Widget>[
    //         CircularProgressIndicator(),
    //         SizedBox(height: 16.0),
    //         Text('Loading file picker...'),
    //       ],
    //     );
    //   },
    // );
  }
}
