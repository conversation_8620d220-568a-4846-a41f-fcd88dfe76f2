import 'package:ddone/services/audio_session_service/audio_session_service_default.dart';
import 'package:ddone/services/audio_session_service/audio_session_service_stub.dart';
import 'package:ddone/utils/screen_util.dart';

abstract class AudioSessionService {
  Future<void> activateRingtoneSession();
  Future<void> activateCallSession();
  Future<void> resetSession();
  bool get interrupted;
}

class AudioSessionServiceFactory {
  static Future<AudioSessionService> create() async {
    if (isAndroid || isIOS || isMacOS) {
      return await AudioSessionServiceDefault.create();
    } else {
      return AudioSessionServiceStub();
    }
  }
}
