import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:ddone/constants/callkit_constants.dart';
import 'package:ddone/events/firebase_event.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/codec_settings_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/priority_stream_manager.dart';
import 'package:ddone/utils/string_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:janus_client/janus_client.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/logger_util.dart';

enum JanusCallStatus {
  nocall,
  ringing,
  incall,
}

const int kJanusServiceJanusEventPriority = 1;
const int kVoipServiceJanusEventPriority = 1;
const int kForegroundServiceJanusEventPriority = 1;
const int kHomeCubitJanusEventPriority = 2;

class JanusServiceException implements Exception {
  final String message;
  const JanusServiceException(this.message);

  @override
  String toString() => 'JanusServiceException: $message';
}

class JanusService {
  // static final singleton pattern
  static final JanusService _instance = JanusService._internal();
  JanusService._internal()
      : _codecSettingsService = sl.get<CodecSettingsService>(),
        _eventBus = sl.get<IEventBus>() {
    // _logTimer = Timer.periodic(const Duration(seconds: 30), (_) => logJanusInfo());
    _eventBus.on<FirebaseEvent>().listen((event) {
      log.t('FirebaseEvent evenbus: ${event.category}');
      if (event.category == 'end-call' || event.category == 'miss-call') {
        // handle situation where user already pressed on decline call button in callkit and waiting for
        // call to proceed (aka waiting for incoming call event), but the call is already ended by caller (miss-call)
        // or already accepted the call in other device (end-call), so we will never receive incoming call event.
        _triggerHasHangUp();
      }
    });
  }
  factory JanusService() {
    return _instance;
  }

  final IEventBus _eventBus;
  final CodecSettingsService _codecSettingsService;

  bool _isInitializing = false;
  bool _isDisposing = false;
  bool _hasIncomingCall = false;
  bool _isHangingUp = false;
  bool _hasHangUp = false;
  bool _isHoldingCall = false;
  bool fromBackground = false;
  // int? _masterId;
  WebSocketJanusTransport? _webSocketJanusTransport;
  JanusClient? _janusClient;
  JanusSession? _janusSession;
  JanusSipPlugin? _janusSipPlugin;
  // JanusSipPlugin? _janusSipPluginHelper;
  Timer? _logTimer;
  dynamic _websocketMessage;
  DateTime? _lastWebsocketMessageReceivedTime;
  RTCVideoRenderer remoteVideoRenderer = RTCVideoRenderer();
  MediaStream? localStream;
  MediaStream? _remoteVideoStream;
  final Map<String, Future<bool> Function()> _mediaOccupiedCallbacks = {};

  final PriorityStreamManager<TypedEvent<JanusEvent>> _janusMessageManager =
      PriorityStreamManager<TypedEvent<JanusEvent>>();

  bool get hasInitialized =>
      _webSocketJanusTransport != null && _janusClient != null && _janusSession != null && _janusSipPlugin != null;

  bool get hasIncomingCall => _hasIncomingCall;

  Stream<TypedEvent<JanusEvent>>? get typedMessages => _janusSipPlugin?.typedMessages;

  /// Establish connection with Janus server
  Future<bool> init(String? sipWsUrl) async {
    if (_isInitializing) return false; // prevent double initialization simultaneously
    if (await checkJanusConnection()) {
      log.t('Janus service init skipped, already has janus connection.');
      return false;
    } // don't initialize when already has a session
    try {
      _isInitializing = true;
      if (sipWsUrl == null || sipWsUrl.trim().isEmpty) {
        sipWsUrl = env!.webSocketUrl;
      }
      _webSocketJanusTransport = WebSocketJanusTransport(url: sipWsUrl);
      _janusClient = JanusClient(
        transport: _webSocketJanusTransport!,
        isUnifiedPlan: true,
      );
      _janusSession = await _janusClient!.createSession();
      _janusSipPlugin = await _janusSession!.attach<JanusSipPlugin>();

      // wait a bit for _janusSipPlugin to be ready.
      bool sipPluginIsReady = await waitForCondition(
          () async => _janusSipPlugin?.remoteTrack != null && _janusSipPlugin?.typedMessages != null);
      if (!sipPluginIsReady) {
        log.w('remoteTrack and typedMessages may not be ready. JanusSipPlugin may have issue.');
      }

      // listen to webcoket connection
      _webSocketJanusTransport?.stream.listen((message) {
        var messageJson = jsonDecode(message);
        _websocketMessage = messageJson;
        _lastWebsocketMessageReceivedTime = DateTime.now();
      });

      // listen to janus event
      _janusMessageManager.startListening(typedMessages);
      addJanusMessageListener(kJanusServiceJanusEventPriority, (event) async {
        Object data = event.event.plugindata?.data;
        log.t('janus service listener data: $data');

        if (data is SipRegisteredEvent) {
          // _masterId = data.result?.masterId;
        } else if (data is SipIncomingCallEvent) {
          bool mediaIsOccupiedWaitResult = await waitForCondition(() async {
            bool mediaIsOccupied = await _isMediaOccupied();
            return !mediaIsOccupied || _hasHangUp;
          }, timeout: const Duration(milliseconds: callkitExpire));
          log.t('mediaIsOccupiedWaitResult:$mediaIsOccupiedWaitResult');
          await handleRemoteJsep(event.jsep);
          if (!_hasHangUp) {
            _hasIncomingCall = true;
          }
        } else if (data is SipAcceptedEvent) {
          await handleRemoteJsep(event.jsep);
          _hasIncomingCall = false;
        } else if (data is SipProgressEvent) {
          await handleRemoteJsep(event.jsep);
        } else if (data is SipCallingEvent) {
        } else if (data is SipProceedingEvent) {
        } else if (data is SipRingingEvent) {
        } else if (data is SipHangupEvent) {
          if (await _isMediaOccupied()) {
            _triggerHasHangUp();
          }
          await stopStreams();
        } else if (data is SipUnRegisteredEvent) {
          _hasIncomingCall = false;
        } else if (data is SipTransferCallEvent) {
        } else if (data is SipMissedCallEvent) {}
        log.t('janus service listener completed');
      }, onError: (error) async {
        log.e('janus message listener error', error: error);
        if (error is JanusError) {}
      });
    } catch (e) {
      log.e('janus init error: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
    return true;
  }

  void addJanusMessageListener(
    int priority,
    FutureOr<void> Function(TypedEvent<JanusEvent>) onData, {
    FutureOr<void> Function(dynamic)? onError,
  }) {
    _janusMessageManager.addListener(priority, onData, onError: onError);
  }

  void removeJanusMessageListener(
    int priority,
    FutureOr<void> Function(TypedEvent<JanusEvent>) onData, {
    FutureOr<void> Function(dynamic)? onError,
  }) {
    _janusMessageManager.removeListener(priority, onData, onError: onError);
  }

  Future<void> initMedia() async {
    await _janusSipPlugin?.initializeWebRTCStack();
    await remoteVideoRenderer.initialize();
    MediaStream? tempVideo = await createLocalMediaStream('remoteVideoStream');
    _remoteVideoStream = tempVideo;
    _janusSipPlugin?.remoteTrack!.listen((event) {
      if (event.track != null && event.flowing == true) {
        _remoteVideoStream?.addTrack(event.track!);
        remoteVideoRenderer.srcObject = _remoteVideoStream;
        if (kIsWeb) {
          // this is done only for web since web api are muted by default for local tagged mediaStream
          remoteVideoRenderer.muted = false;
        }
      }
    });
  }

  Future<void> initLocalStream() async {
    MediaStream? tempLocalStream =
        await _janusSipPlugin!.initializeMediaDevices(mediaConstraints: {'audio': true, 'video': false});
    localStream = tempLocalStream;
  }

  Future<void> dispose() async {
    await waitForCondition(() async => !_isDisposing, timeout: const Duration(milliseconds: 3000));
    if (_janusSipPlugin == null && _janusSession == null && _webSocketJanusTransport == null) {
      log.t('JanusService already disposed, skipping');
      return;
    }

    try {
      log.t('disposing janus in janusService');
      _isDisposing = true;
      try {
        await _janusSipPlugin?.unregister();
        await _janusSipPlugin?.dispose();
      } on PlatformException catch (e) {
        if (e.code == 'MediaStreamDisposeFailed') {
          // looks like is janus_plugin bug, specific to windows app.
          // doesn't affect anything, so just catch it, log it as warn, and let it continue to run.
          log.w('MediaStreamDisposeFailed', error: e);
        } else {
          rethrow;
        }
      } on NoSuchMethodError catch (e) {
        if (e.toString().contains('[]("janus")')) {
          // When we already disconnected for janus server and we want to again unregister from it
          // janus server will return null as response. This cause the JanusEvent.fromJson() in unregister method
          // throw: "The method '[]' was called on null. ...... Tried calling: []("janus")' error."
          // So can just ignore this because it is means that we already unregistered.
          log.w('NoSuchMethodError', error: e);
        } else {
          rethrow;
        }
      } catch (e) {
        final errorMessage = e.toString();
        if (errorMessage.contains('getTransceivers() peerConnection is null') ||
            errorMessage.contains('MediaStreamTrack has been disposed')) {
          // In windows app when user decline call, _janusSipPlugin.peerConnection will be null.
          // Seems like is janus_plugin bug in windows platform, so just catch it and log it as warn.
          // As long as later _janusSipPlugin get reinitialized, then it will be fine.
          // Also handle MediaStreamTrack disposal errors during service shutdown
          log.w('WebRTC/MediaStream error during disposal', error: errorMessage);
        } else {
          rethrow;
        }
      }
      _webSocketJanusTransport?.dispose();
      _janusSession?.dispose();
      _janusMessageManager.dispose();

      _webSocketJanusTransport = null;
      _janusClient = null;
      _janusSession = null;
      _janusSipPlugin = null;
      _hasIncomingCall = false;
      _isHoldingCall = false;
    } catch (e) {
      log.e('Failed to dispose JanusService', error: e);
      rethrow;
    } finally {
      _isDisposing = false;
      log.t('disposing janus in janusService complete');
    }
  }

  Future<void> disposeMedia() async {
    _remoteVideoStream?.dispose();
    try {
      remoteVideoRenderer.srcObject = null;
      remoteVideoRenderer.dispose();
    } catch (e) {
      final errorMessage = e.toString();
      if (errorMessage.contains('The RTCVideoRenderer is disposed')) {
        log.w('Failed in disposeMedia', error: e);
      } else {
        rethrow;
      }
    }
  }

  Future<void>? handleRemoteJsep(RTCSessionDescription? data) => _janusSipPlugin?.handleRemoteJsep(data);

  Future<void> register(String username, String proxy, String secret, String? sipName, String? sipHeaderToken,
      {String? masterId}) async {
    log.t(
        'register janus sip with username: $username, proxy: $proxy, sipHeaderToken: $sipHeaderToken, masterId: $masterId');
    await _janusSipPlugin!.register(
      username,
      displayName: sipName,
      forceTcp: true,
      rfc2543Cancel: true,
      proxy: proxy,
      secret: secret,
      userAgent: 'DDOne $sipHeaderToken',
      registerTtl: 300, // 5min

      // Can't use masterId yet, because bug in janusSipPlugin, this should be an int but plugin take in as string
      // So now don't use helper session, just use 2 different session will do. Should be ok as long as we don't have
      // hanging orphan session.
      // type: masterId != null ? 'helper' : null,
      // masterId: masterId,
    );

    // TRY: create another helper session

    // await Future.delayed(const Duration(seconds: 3));
    // _janusSipPluginHelper = await _janusSession!.attach<JanusSipPlugin>();
    // bool sipPluginHelperIsReady = await waitForCondition(
    //     () async => _janusSipPluginHelper?.remoteTrack != null && _janusSipPluginHelper?.typedMessages != null);
    // if (!sipPluginHelperIsReady) {
    //   log.w('remoteTrack and typedMessages may not be ready. JanusSipPlugin helper may have issue.');
    // }
    // _janusSipPluginHelper!.typedMessages?.listen((event) {
    //   Object data = event.event.plugindata?.data;
    //   log.t('janus service helper listener data: $data');
    // });
    // bool hasMasterId = await waitForCondition(() async => _masterId != null);
    // if (!hasMasterId) {
    //   log.w('masterId is null, cannot create helper session.');
    //   return;
    // }
    // await _janusSipPluginHelper!.register(
    //   username,
    //   displayName: sipName,
    //   forceTcp: true,
    //   rfc2543Cancel: true,
    //   proxy: proxy,
    //   secret: secret,
    //   type: 'helper',
    //   masterId: _masterId,
    // );
    // log.t('created helper session.');
  }

  Future<void> makeCall(
    String extNum,
    String sipProxy,
  ) async {
    try {
      // create offer
      await _setAudioCodec();
      RTCSessionDescription offer = await _janusSipPlugin!.createOffer(
        videoRecv: false,
        audioRecv: true,
      );

      if (isAndroid) {
        final modifiedSdp = _setAudioCodecWithSdpManipulation(offer.sdp);
        if (modifiedSdp != null) {
          offer = RTCSessionDescription(modifiedSdp, offer.type);
        }
        await _janusSipPlugin!.webRTCHandle!.peerConnection!.setLocalDescription(offer);
      }

      // make call
      await _janusSipPlugin!.call(
        'sip:${extNum.replaceAll(' ', '').trim()}@$sipProxy',
        offer: offer,
        autoAcceptReInvites: false,
      );
    } catch (e) {
      log.e('Failed to make call', error: e);
    }
  }

  Future<void> acceptCall() async {
    bool waitResult = await waitForCondition(() async => _hasIncomingCall || _hasHangUp,
        timeout: const Duration(milliseconds: callkitExpire));
    if (waitResult && !_hasHangUp) {
      await _setAudioCodec();
      RTCSessionDescription answer = await _janusSipPlugin!.createAnswer();

      if (isAndroid) {
        final modifiedSdp = _setAudioCodecWithSdpManipulation(answer.sdp);
        if (modifiedSdp != null) {
          answer = RTCSessionDescription(modifiedSdp, answer.type);
        }
      }

      await _janusSipPlugin!.accept(sessionDescription: answer);
    } else {
      throw const JanusServiceException('No incoming call to accept');
    }
  }

  /// This use flutter_webrtc API method to set codec
  Future<void> _setAudioCodec() async {
    // Get user-configured codec preferences
    final preferredCodecs = _codecSettingsService.getConfiguredCodecs();
    log.t('_setAudioCodec - user preferred codecs: $preferredCodecs');

    // Get available audio codec capabilities
    final capabilities = await getRtpSenderCapabilities('audio');
    if (capabilities.codecs == null) {
      log.w('_setAudioCodec - no audio codec capabilities available');
      return;
    }

    // Map user-configured codec names to available codec capabilities
    final List<RTCRtpCodecCapability> orderedCodecs = [];
    for (final codecName in preferredCodecs) {
      final codecCapability = capabilities.codecs!
          .firstWhereOrNull((codec) => codec.mimeType.toLowerCase() == 'audio/${codecName.toLowerCase()}');
      if (codecCapability != null) {
        orderedCodecs.add(codecCapability);
      } else {
        log.w('_setAudioCodec - codec not available: $codecName');
      }
    }

    // Skip if no valid codecs are found
    if (orderedCodecs.isEmpty) {
      log.w('_setAudioCodec - no valid codecs found, using default');
      return;
    }

    // Get peer connection
    RTCPeerConnection? peerConnection = _janusSipPlugin!.peerConnection;
    if (peerConnection == null) {
      log.w('_setAudioCodec - no peer connection available');
      return;
    }

    // Get all transceivers
    final transceivers = await peerConnection.getTransceivers();
    final audioTransceivers = transceivers
        .where(
          (transceiver) => transceiver.receiver.track?.kind == 'audio' || transceiver.sender.track?.kind == 'audio',
        )
        .toList();

    if (audioTransceivers.isEmpty) {
      log.w('_setAudioCodec - no audio transceivers found');
      return;
    }

    // Set codec preferences for all audio transceivers
    for (final audioTransceiver in audioTransceivers) {
      await audioTransceiver.setCodecPreferences(orderedCodecs);
    }
  }

  /// Helper function to manipulate SDP to prioritize a list of codecs.
  /// - Need this because _setAudioCodec does not work in android, seems like is a bug in flutter_webrtc.
  String? _setAudioCodecWithSdpManipulation(String? sdp) {
    // return sdp;
    if (sdp == null) return null;

    // Get the user-configured codec priority order.
    final codecSettingsService = sl.get<CodecSettingsService>();
    final preferredCodecs = codecSettingsService.getConfiguredCodecs();

    final lines = sdp.split('\r\n');
    final mLineIndex = lines.indexWhere((line) => line.startsWith('m=audio'));
    if (mLineIndex == -1) {
      log.w('No m=audio line found in SDP');
      return sdp; // No audio line found, return as is.
    }

    // Map all available payload types to their codec names from the SDP.
    final Map<String, String> payloadToCodec = {};
    final rtpmapRegex = RegExp(r'a=rtpmap:(\d+)\s+([a-zA-Z0-9\-]+)/');

    for (final line in lines) {
      final match = rtpmapRegex.firstMatch(line);
      if (match != null) {
        final payload = match.group(1)!;
        final codecName = match.group(2)!;
        payloadToCodec[payload] = codecName;
      }
    }

    if (payloadToCodec.isEmpty) {
      log.w('No codecs found in SDP');
      return sdp; // No codecs defined.
    }

    final mLineParts = lines[mLineIndex].split(' ');
    final originalPayloads = mLineParts.sublist(3);

    final List<String> orderedPayloads = [];
    final Set<String> allowedPayloads = <String>{};

    // Create the new list of payloads based on our preferred order.
    for (final codecName in preferredCodecs) {
      for (final payload in originalPayloads) {
        // Check if the payload's codec name matches our preferred codec (case-insensitive).
        if (payloadToCodec[payload]?.toLowerCase() == codecName.toLowerCase()) {
          if (!orderedPayloads.contains(payload)) {
            orderedPayloads.add(payload);
            allowedPayloads.add(payload);
          }
        }
      }
    }

    if (orderedPayloads.isEmpty) {
      log.e('No matching codecs found! Available: ${payloadToCodec.values.toList()}, Preferred: $preferredCodecs');
      return sdp; // Could not reorder - return original SDP
    }

    // Rebuild the m=audio line with only preferred codecs
    final newMLine = [...mLineParts.sublist(0, 3), ...orderedPayloads].join(' ');
    lines[mLineIndex] = newMLine;

    // Remove rtpmap and fmtp lines for codecs that are not in our allowed list
    final filteredLines = <String>[];
    for (final line in lines) {
      bool shouldKeepLine = true;

      // Check if this is an rtpmap or fmtp line for a codec we're removing
      final rtpmapMatch = RegExp(r'a=rtpmap:(\d+)').firstMatch(line);
      final fmtpMatch = RegExp(r'a=fmtp:(\d+)').firstMatch(line);
      final rtcpFbMatch = RegExp(r'a=rtcp-fb:(\d+)').firstMatch(line);

      if (rtpmapMatch != null) {
        final payload = rtpmapMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      } else if (fmtpMatch != null) {
        final payload = fmtpMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      } else if (rtcpFbMatch != null) {
        final payload = rtcpFbMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      }

      if (shouldKeepLine) {
        filteredLines.add(line);
      }
    }

    final modifiedSdp = filteredLines.join('\r\n');
    return modifiedSdp;
  }

  Future<void> transferCall(String extNum, String sipProxy) async {
    await _janusSipPlugin?.transfer('sip:$extNum@$sipProxy');
  }

  Future<void>? declineCall() async {
    bool waitResult = await waitForCondition(() async {
      return _hasIncomingCall || _hasHangUp;
    }, timeout: const Duration(milliseconds: callkitExpire));
    if (waitResult && !_hasHangUp) {
      await _janusSipPlugin?.decline();
    } else {
      log.w('No incomming call to decline');
    }
  }

  Future<void>? holdCall() async {
    try {
      _isHoldingCall = true;
      return _janusSipPlugin?.hold(SipHoldState.SENDONLY);
    } catch (e) {
      log.e('Failed to holdCall', error: e);
      _isHoldingCall = false;
    }
  }

  Future<void>? unholdCall() async {
    try {
      _isHoldingCall = false;
      return _janusSipPlugin?.unhold();
    } catch (e) {
      log.e('Failed to unholdCall', error: e);
      _isHoldingCall = true;
    }
  }

  Future<void>? toggleHoldCall() async {
    if (_isHoldingCall) {
      return unholdCall();
    } else {
      return holdCall();
    }
  }

  Future<void>? hangupCall() async {
    if (_isHangingUp) {
      log.w('hanging up... Skipped hangupCall');
      return;
    }
    _isHangingUp = true;
    await _janusSipPlugin?.hangup();
    _isHangingUp = false;
  }

  /// Mute current device mic, such that opposite device won't receive any sound.
  Future<void> muteMicAudio(bool mute) async {
    log.t('muteMicAudio - mute:$mute');
    var senders = await _janusSipPlugin?.webRTCHandle?.peerConnection?.senders;
    senders?.forEach((element) {
      if (element.track?.kind == 'audio') {
        element.track?.enabled = !mute;
      }
    });
  }

  /// Mute current device speaker, such that current device won't receive any sound.
  Future<void> muteSpeakerAudio(bool mute) async {
    log.t('muteSpeakerAudio - mute:$mute');
    var receivers = await _janusSipPlugin?.webRTCHandle?.peerConnection?.receivers;
    receivers?.forEach((element) {
      if (element.track?.kind == 'audio') {
        element.track?.enabled = !mute;
      }
    });
  }

  void sendDTMF(String tone) async {
    try {
      RTCPeerConnection peerConnection = _janusSipPlugin!.webRTCHandle!.peerConnection!;
      List<RTCRtpSender> senders = await peerConnection.getSenders();
      RTCRtpSender audioSender = senders.firstWhere(
        (sender) => sender.track?.kind == 'audio',
        orElse: () => throw Exception('No audio sender found'),
      );
      if (isWindows) {
        RTCDTMFSender? dtmfSender = audioSender.dtmfSender;
        dtmfSender.insertDTMF(tone);
      } else {
        MediaStreamTrack? audioTrack = audioSender.track;
        if (audioTrack == null) {
          throw Exception('Audio track not found');
        }
        RTCDTMFSender dtmfSender = peerConnection.createDtmfSender(audioTrack);
        dtmfSender.insertDTMF(tone);
      }
    } catch (e) {
      log.e('Failed to send DTMF', error: e);
    }
  }

  Future<void> clearRtcPeerConnection() async {
    await _janusSipPlugin?.webRTCHandle?.peerConnection?.close();
    await _janusSipPlugin?.webRTCHandle?.peerConnection?.dispose();
  }

  Future<void> stopStreams() async {
    if (isWindows) {
      // in windows not sure why it won't release the mic if don't have this.
      await clearRtcPeerConnection();
    }

    // stop all the track (newer version of flutter_janus_client has this)
    for (MediaStreamTrack track in localStream?.getTracks() ?? []) {
      await _safeStopTrack(track);
    }
    for (MediaStreamTrack track in _remoteVideoStream?.getTracks() ?? []) {
      await _safeStopTrack(track);
    }

    // dispose it
    await Future.wait([
      _disposeMediaStream(localStream),
      _disposeMediaStream(_remoteVideoStream),
    ]);
    localStream = null;
    _remoteVideoStream = null;
  }

  Future<void> _safeStopTrack(MediaStreamTrack mediaStreamTrack) async {
    try {
      await mediaStreamTrack.stop();
    } catch (e) {
      // Handle the case where MediaStreamTrack has already been disposed
      // This can happen during background service shutdown when Flutter engine is being destroyed
      if (e.toString().contains('MediaStreamTrack has been disposed') ||
          e.toString().contains('MediaStreamDisposeFailed')) {
        log.w('MediaStream already disposed, skipping: $e');
      } else {
        rethrow;
      }
    }
  }

  Future<void> _disposeMediaStream(MediaStream? mediaStream) async {
    if (mediaStream == null) return;
    try {
      await mediaStream.dispose();
    } catch (e) {
      // Handle the case where MediaStream has already been disposed
      if (e.toString().contains('MediaStream has been disposed') || e.toString().contains('MediaStreamDisposeFailed')) {
        log.w('MediaStream already disposed, skipping: $e');
      } else {
        rethrow;
      }
    }
  }

  Future<bool> checkJanusConnection() async {
    try {
      if (_webSocketJanusTransport == null ||
          _janusClient == null ||
          _janusSession == null ||
          _janusSipPlugin == null) {
        return false;
      }
      // this check whether websocket is connected.
      bool websocketIsConnected = _webSocketJanusTransport?.isConnected ?? false;
      if (!websocketIsConnected) {
        log.t('websocket is not connected');
        return false;
      }
      // check when does we last receive websocket message from janus server.
      // too long no message means that we already disconnected from janus server.
      // - JanusClient send keep alive message every 50 seconds.
      if (_lastWebsocketMessageReceivedTime != null &&
          DateTime.now().difference(_lastWebsocketMessageReceivedTime!).inSeconds > 60) {
        log.t('Too long no message from janus server.');
        return false;
      }
      // this check whether the session has error.
      String websocketMessageStatus = _websocketMessage?['janus'];
      bool janusSessionError = websocketMessageStatus == 'error';
      if (janusSessionError) {
        log.t('janus session recoded error. _websocketMessage:$_websocketMessage');
        return false;
      }
      return true;
    } catch (e) {
      log.w('checkJanusConnection error', error: e);
      return false;
    }
  }

  Future<JanusCallStatus> checkJanusCallStatus() async {
    List<StatsReport>? statsReports = await _janusSipPlugin?.peerConnection?.getStats();
    if (statsReports != null && statsReports.length > 1) {
      // when there is no call, we only have one 'peer-connection' type of stat.
      // when it is ringing, we won't have 'local-candidate' type of stat, but got some other stat.
      // when it is in call, we will have 'local-candidate' type of stat
      StatsReport? localCandidateStats = statsReports.firstWhereOrNull((s) => s.type == 'local-candidate');
      if (localCandidateStats == null) {
        return JanusCallStatus.ringing;
      } else {
        return JanusCallStatus.incall;
      }
    }
    return JanusCallStatus.nocall;
  }

  Future<bool> waitJanusConnect() async {
    return waitForCondition(() async => checkJanusConnection(), timeout: const Duration(milliseconds: 5000));
  }

  Future<bool> isInCall() async {
    final stats = await _janusSipPlugin?.peerConnection?.getStats();
    if (stats != null && stats.length > 1) {
      // when there is no call, we only have one 'peer-connection' type of stat.
      // otherwise we will have a lot more.
      return true;
    }
    return false;
  }

  /// Provide callback for janusService to determine whether media has been occupied.
  /// The callback should return true if media is occupied.
  void mediaOccupiedCallback(String key, Future<bool> Function() callback) async {
    _mediaOccupiedCallbacks[key] = callback;
  }

  Future<bool> _isMediaOccupied() async {
    if (_mediaOccupiedCallbacks.isEmpty) return false;
    final List<Future<bool>> futures = [];
    for (Future<bool> Function() callback in _mediaOccupiedCallbacks.values) {
      futures.add(callback());
    }
    try {
      final List<bool> results = await Future.wait(futures);
      if (results.firstWhereOrNull((result) => result == true) != null) {
        return true;
      }
    } catch (e) {
      log.e('Failed to run mediaOccupiedCallbacks', error: e);
    }
    return false;
  }

  Future<void> _triggerHasHangUp() async {
    _hasHangUp = true;
    await Future.delayed(const Duration(milliseconds: 250));
    _hasHangUp = false;
  }

  /// Use to print out info that we can get from Janus server
  Future<void> logJanusInfo() async {
    try {
      final webSocketJanusTransport = WebSocketJanusTransport(url: 'wss://rtc.dotdashtech.com:8989/ws');
      final janusClient = JanusClient(
        transport: webSocketJanusTransport,
        isUnifiedPlan: true,
      );
      // this can only work before creating session
      JanusClientInfo? janusClientInfo = await janusClient.getInfo();
      log.d('logJanusInfo - janusClient - info:${StringUtil.prettyPrint(janusClientInfo.toJson())}');
    } on TypeError catch (e) {
      log.w('Failed to getInfo with janusClient.', error: e);
    }

    final websocketIsConnected = _webSocketJanusTransport?.isConnected;
    log.d('logJanusInfo - websocketJanusTransport - isConnected:$websocketIsConnected');
    log.d('logJanusInfo - websocketJanusTransport - messages:$_websocketMessage');

    final sessionId = _janusSession?.sessionId;
    log.d('logJanusInfo - janusSession - sessionId:$sessionId');
    final handleId = _janusSipPlugin?.handleId;
    final plugin = _janusSipPlugin?.plugin;
    final pollingActive = _janusSipPlugin?.pollingActive;
    final connectionState = await _janusSipPlugin?.peerConnection?.getConnectionState();
    final signalingState = await _janusSipPlugin?.peerConnection?.getSignalingState();
    final stats = await _janusSipPlugin?.peerConnection?.getStats();
    log.d(
        'logJanusInfo - janusSipPlugin - handleId:$handleId, plugin:$plugin, pollingActive:$pollingActive, connectionState:$connectionState, signalingState:$signalingState');
    if (stats != null) {
      for (StatsReport stat in stats) {
        log.d(
            'logJanusInfo - janusSipPlugin - stats.id:${stat.id}, stats.type:${stat.type}, stats.timestamp:${stat.timestamp}, stats.values:${StringUtil.prettyPrint(stat.values)}');
      }
    }
  }

  static String extractExtFromUsername(String username) {
    final startIndex = username.indexOf('sip:');
    if (startIndex == -1) {
      return username; // Return original username if 'sip:' is not found
    }
    final atIndex = username.indexOf('@');
    if (atIndex == -1) {
      return username; // Return original username if '@' is not found
    }
    final number = username.substring(startIndex + 4, atIndex); // +4 to skip 'sip:'
    return number;
  }
}
