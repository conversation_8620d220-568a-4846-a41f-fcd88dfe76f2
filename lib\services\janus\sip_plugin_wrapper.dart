import 'package:collection/collection.dart';
import 'package:ddone/constants/voip_constants.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/models/sip_transfer_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/codec_settings_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/priority_stream_manager.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:janus_client/janus_client.dart';
import 'package:rxdart/rxdart.dart';

class SipPluginWrapper {
  final JanusSipPlugin _janusSipPlugin;
  bool _isHelper = false;
  int? _masterId;
  SipCallState? _prevSipCallState;
  final _sipCallStateController = BehaviorSubject<SipCallState>();
  final PriorityStreamManager<TypedEvent<JanusEvent>> _priorityStreamManager =
      PriorityStreamManager<TypedEvent<JanusEvent>>();
  final CodecSettingsService _codecSettingsService;

  Stream<SipCallState> get sipCallStateStream => _sipCallStateController.stream.distinct();
  SipCallState get currSipCallState => _sipCallStateController.value;
  SipCallState get prevSipCallState => _prevSipCallState ?? currSipCallState;
  PriorityStreamManager<TypedEvent<JanusEvent>> get priorityTypedMessages => _priorityStreamManager;
  int? get handleId => _janusSipPlugin.handleId;
  bool get isMaster => !_isHelper;
  bool get isHelper => _isHelper;
  int? get masterId => _masterId;

  SipPluginWrapper(this._janusSipPlugin) : _codecSettingsService = sl.get<CodecSettingsService>() {
    _sipCallStateController.add(SipCallState(
      handleId: handleId!,
      isHelper: _isHelper,
      status: SipPluginStatus.initial,
      callId: '',
      counterPartUri: '',
      counterPartName: '',
      startTimestamp: DateTime.now().microsecondsSinceEpoch,
    ));
    _priorityStreamManager.startListening(_janusSipPlugin.typedMessages);
    _priorityStreamManager.addListener(kJanusSipPluginEventPriority, _sipPluginDataListener,
        onError: _sipPluginErrorListener);
  }

  Future<void> _sipPluginDataListener(TypedEvent<JanusEvent> event) async {
    Object data = event.event.plugindata?.data;
    log.t('SIP plugin handleId: $handleId - typedEvent listener data: $data');
    String? counterPartUri;
    String? counterPartName;
    if (data is SipRegisteredEvent) {
      _masterId = data.result?.masterId;
      _updateSipPluginStatus(SipPluginStatus.registered);
    } else if (data is SipIncomingCallEvent) {
      counterPartUri = data.result?.username;
      counterPartName = data.result?.displayname;
      _updateSipCallState(
        callId: data.callId,
        counterPartUri: counterPartUri,
        counterPartName: counterPartName,
      );
      await _janusSipPlugin.handleRemoteJsep(event.jsep);
      _updateSipPluginStatus(SipPluginStatus.incomingCall);
    } else if (data is SipAcceptedEvent) {
      _updateSipCallState(callId: data.callId);
      await _janusSipPlugin.handleRemoteJsep(event.jsep);
      _updateSipPluginStatus(SipPluginStatus.accepted);
    } else if (data is SipProgressEvent) {
      _updateSipCallState(callId: data.callId);
      await _janusSipPlugin.handleRemoteJsep(event.jsep);
      _updateSipPluginStatus(SipPluginStatus.progress);
    } else if (data is SipCallingEvent) {
      _updateSipCallState(callId: data.callId);
      _updateSipPluginStatus(SipPluginStatus.calling);
    } else if (data is SipProceedingEvent) {
      _updateSipCallState(callId: data.callId);
      _updateSipPluginStatus(SipPluginStatus.proceeding);
    } else if (data is SipRingingEvent) {
      _updateSipCallState(callId: data.callId);
      _updateSipPluginStatus(SipPluginStatus.ringing);
    } else if (data is SipHangupEvent) {
      await _stopTracks();
      if (isMaster) {
        await _resetPeerConnection();
        await _reinitWebRTCStack();
      }
      _updateSipPluginStatus(SipPluginStatus.hungup);
    } else if (data is SipUnRegisteredEvent) {
      _updateSipPluginStatus(SipPluginStatus.unregistered);
    } else if (data is SipTransferCallEvent) {
      _updateSipPluginStatus(SipPluginStatus.transfer);
    } else if (data is SipMissedCallEvent) {
      _updateSipCallState(callId: data.callId);
      _updateSipPluginStatus(SipPluginStatus.missedCall);
    }
    log.t('SIP plugin handleId: $handleId - listener completed');
  }

  Future<void> _sipPluginErrorListener(dynamic error) async {
    log.e('SIP plugin handleId: $handleId - listener error', error: error);
    if (error is JanusError) {
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  /// Update status should use this.
  SipPluginStatus _updateSipPluginStatus(SipPluginStatus newStatus) {
    if (newStatus.value >= currSipCallState.status.value) {
      _updateSipCallState(status: newStatus);
    }
    return currSipCallState.status;
  }

  /// Update anything other than status should use this.
  /// For status, use _updateSipPluginStatus
  void _updateSipCallState({
    bool? isHelper,
    String? callId,
    String? counterPartUri,
    String? counterPartName,
    bool? onhold,
    bool? localAudioDisabled,
    bool? remoteAudioDisabled,
    SipTransferModel? transferData,
    SipPluginStatus? status, // DONT USE THIS, WE WANT TO MAINTAIN STATE
  }) {
    SipCallState newSipCallState = currSipCallState.copyWith(
      isHelper: isHelper ?? currSipCallState.isHelper,
      status: status ?? currSipCallState.status,
      callId: callId ?? currSipCallState.callId,
      counterPartUri: counterPartUri ?? currSipCallState.counterPartUri,
      counterPartName: counterPartName ?? currSipCallState.counterPartName,
      onhold: onhold ?? currSipCallState.onhold,
      localAudioDisabled: localAudioDisabled ?? currSipCallState.localAudioDisabled,
      remoteAudioDisabled: remoteAudioDisabled ?? currSipCallState.remoteAudioDisabled,
      transferData: transferData ?? currSipCallState.transferData,
    );
    if (currSipCallState != newSipCallState) {
      _prevSipCallState = currSipCallState;
    }
    _sipCallStateController.add(newSipCallState);
  }

  Future<void> addTrackToPeerConnection(MediaStream localMediaStream) async {
    for (var track in localMediaStream.getTracks()) {
      await _janusSipPlugin.webRTCHandle!.peerConnection!.addTrack(track, localMediaStream);
    }
  }

  Future<void> _stopTracks() async {
    var remoteStream = _janusSipPlugin.webRTCHandle?.remoteStream;
    if (remoteStream != null) {
      await stopAllTracks(remoteStream);
      await remoteStream.dispose();
      _janusSipPlugin.webRTCHandle?.remoteStream = null;
    }
    RTCPeerConnection? peerConnection = _janusSipPlugin.webRTCHandle!.peerConnection;
    if (peerConnection != null) {
      List<RTCRtpSender> senders = await peerConnection.senders;
      for (var sender in senders) {
        await peerConnection.removeTrack(sender);
      }
    }
  }

  Future<void> _resetPeerConnection() async {
    if (_janusSipPlugin.webRTCHandle?.peerConnection != null) {
      try {
        await _janusSipPlugin.webRTCHandle!.peerConnection!.close();
        await _janusSipPlugin.webRTCHandle!.peerConnection!.dispose();
        _janusSipPlugin.webRTCHandle!.peerConnection = null;
      } catch (e) {
        log.w('SIP plugin handleId: $handleId - Failed to close/dispose peerConnection', error: e);
      }
    }
  }

  Future<void> _reinitWebRTCStack() async {
    log.t('SIP plugin handleId: $handleId - reinitWebRTCStack');
    await _janusSipPlugin.initializeWebRTCStack();
    _updateSipCallState(
      callId: '',
      counterPartUri: '',
      counterPartName: '',
      onhold: false,
      localAudioDisabled: false,
      remoteAudioDisabled: false,
      status: SipPluginStatus.registered, // the only exception that we can update status with _updateSipCallState
    );
  }

  /// Register SIP plugin with Janus server.
  /// If masterId is provided, this SIP plugin will be registered as a helper session.
  Future<void> register({
    required String username,
    String? proxy,
    String? secret,
    String? sipName,
    String? sipHeaderToken,
    int? masterId,
  }) async {
    try {
      log.t(
          'SIP plugin handleId: $handleId - Register with username: $username, proxy: $proxy, sipName: $sipName, masterId: $masterId');
      if (masterId == null) {
        _isHelper = false;
      } else {
        _isHelper = true;
      }
      await _janusSipPlugin.register(
        username,
        displayName: sipName,
        forceTcp: true,
        rfc2543Cancel: true,
        proxy: proxy,
        secret: secret,
        userAgent: 'DDOne $sipHeaderToken',
        registerTtl: kSipRegisterTtl,
        type: _isHelper ? 'helper' : null,
        masterId: masterId,
      );
      _updateSipCallState(isHelper: _isHelper);
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to register SIP plugin', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> unregister() async {
    try {
      await _janusSipPlugin.unregister();
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to unregister SIP plugin', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> dispose() async {
    if (currSipCallState.status.value >= SipPluginStatus.disposing.value) {
      log.w('SIP plugin handleId: $handleId - disposing. Skipped dispose');
      return;
    }
    try {
      _updateSipPluginStatus(SipPluginStatus.disposing);
      _priorityStreamManager.dispose();
      await _janusSipPlugin.detach();
      await _janusSipPlugin.dispose();
      _updateSipPluginStatus(SipPluginStatus.disposed);
      log.t('SIP plugin handleId: $handleId - disposed');
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to dispose SIP plugin', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
    await _sipCallStateController.close();
  }

  Future<void> call({
    required String displayName,
    required String extNum,
    required String sipProxy,
  }) async {
    log.t('SIP plugin handleId: $handleId - call with: extNum=$extNum, sipProxy=$sipProxy');
    final uri = buildSipUri(extNum, sipProxy);
    _updateSipCallState(
      counterPartUri: uri,
      counterPartName: displayName,
    );
    _updateSipPluginStatus(SipPluginStatus.callInitiated);

    try {
      // create offer
      await _setAudioCodec();
      RTCSessionDescription offer = await _janusSipPlugin.createOffer(
        videoRecv: false,
        audioRecv: true,
      );

      if (isAndroid) {
        final modifiedSdp = _setAudioCodecWithSdpManipulation(offer.sdp);
        if (modifiedSdp != null) {
          offer = RTCSessionDescription(modifiedSdp, offer.type);
        }
        await _janusSipPlugin.webRTCHandle!.peerConnection!.setLocalDescription(offer);
      }

      await _janusSipPlugin.call(
        uri,
        offer: offer,
        autoAcceptReInvites: false,
      );
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to make call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> accept() async {
    await _setAudioCodec();
    RTCSessionDescription answer = await _janusSipPlugin.createAnswer();

    if (isAndroid) {
      final modifiedSdp = _setAudioCodecWithSdpManipulation(answer.sdp);
      if (modifiedSdp != null) {
        answer = RTCSessionDescription(modifiedSdp, answer.type);
      }
    }

    await _janusSipPlugin.accept(sessionDescription: answer);
  }

  /// This use flutter_webrtc API method to set codec
  Future<void> _setAudioCodec() async {
    // Get user-configured codec preferences
    final preferredCodecs = _codecSettingsService.getConfiguredCodecs();
    log.t('_setAudioCodec - user preferred codecs: $preferredCodecs');

    // Get available audio codec capabilities
    final capabilities = await getRtpSenderCapabilities('audio');
    if (capabilities.codecs == null) {
      log.w('_setAudioCodec - no audio codec capabilities available');
      return;
    }

    // Map user-configured codec names to available codec capabilities
    final List<RTCRtpCodecCapability> orderedCodecs = [];
    for (final codecName in preferredCodecs) {
      final codecCapability = capabilities.codecs!
          .firstWhereOrNull((codec) => codec.mimeType.toLowerCase() == 'audio/${codecName.toLowerCase()}');
      if (codecCapability != null) {
        orderedCodecs.add(codecCapability);
      } else {
        log.w('_setAudioCodec - codec not available: $codecName');
      }
    }

    // Skip if no valid codecs are found
    if (orderedCodecs.isEmpty) {
      log.w('_setAudioCodec - no valid codecs found, using default');
      return;
    }

    // Get peer connection
    RTCPeerConnection? peerConnection = _janusSipPlugin.peerConnection;
    if (peerConnection == null) {
      log.w('_setAudioCodec - no peer connection available');
      return;
    }

    // Get all transceivers
    final transceivers = await peerConnection.getTransceivers();
    final audioTransceivers = transceivers
        .where(
          (transceiver) => transceiver.receiver.track?.kind == 'audio' || transceiver.sender.track?.kind == 'audio',
        )
        .toList();

    if (audioTransceivers.isEmpty) {
      log.w('_setAudioCodec - no audio transceivers found');
      return;
    }

    // Set codec preferences for all audio transceivers
    for (final audioTransceiver in audioTransceivers) {
      await audioTransceiver.setCodecPreferences(orderedCodecs);
    }
  }

  /// Helper function to manipulate SDP to prioritize a list of codecs.
  /// - Need this because _setAudioCodec does not work in android, seems like is a bug in flutter_webrtc.
  String? _setAudioCodecWithSdpManipulation(String? sdp) {
    // return sdp;
    if (sdp == null) return null;

    // Get the user-configured codec priority order.
    final preferredCodecs = _codecSettingsService.getConfiguredCodecs();

    final lines = sdp.split('\r\n');
    final mLineIndex = lines.indexWhere((line) => line.startsWith('m=audio'));
    if (mLineIndex == -1) {
      log.w('No m=audio line found in SDP');
      return sdp; // No audio line found, return as is.
    }

    // Map all available payload types to their codec names from the SDP.
    final Map<String, String> payloadToCodec = {};
    final rtpmapRegex = RegExp(r'a=rtpmap:(\d+)\s+([a-zA-Z0-9\-]+)/');

    for (final line in lines) {
      final match = rtpmapRegex.firstMatch(line);
      if (match != null) {
        final payload = match.group(1)!;
        final codecName = match.group(2)!;
        payloadToCodec[payload] = codecName;
      }
    }

    if (payloadToCodec.isEmpty) {
      log.w('No codecs found in SDP');
      return sdp; // No codecs defined.
    }

    final mLineParts = lines[mLineIndex].split(' ');
    final originalPayloads = mLineParts.sublist(3);

    final List<String> orderedPayloads = [];
    final Set<String> allowedPayloads = <String>{};

    // Create the new list of payloads based on our preferred order.
    for (final codecName in preferredCodecs) {
      for (final payload in originalPayloads) {
        // Check if the payload's codec name matches our preferred codec (case-insensitive).
        if (payloadToCodec[payload]?.toLowerCase() == codecName.toLowerCase()) {
          if (!orderedPayloads.contains(payload)) {
            orderedPayloads.add(payload);
            allowedPayloads.add(payload);
          }
        }
      }
    }

    if (orderedPayloads.isEmpty) {
      log.e('No matching codecs found! Available: ${payloadToCodec.values.toList()}, Preferred: $preferredCodecs');
      return sdp; // Could not reorder - return original SDP
    }

    // Rebuild the m=audio line with only preferred codecs
    final newMLine = [...mLineParts.sublist(0, 3), ...orderedPayloads].join(' ');
    lines[mLineIndex] = newMLine;

    // Remove rtpmap and fmtp lines for codecs that are not in our allowed list
    final filteredLines = <String>[];
    for (final line in lines) {
      bool shouldKeepLine = true;

      // Check if this is an rtpmap or fmtp line for a codec we're removing
      final rtpmapMatch = RegExp(r'a=rtpmap:(\d+)').firstMatch(line);
      final fmtpMatch = RegExp(r'a=fmtp:(\d+)').firstMatch(line);
      final rtcpFbMatch = RegExp(r'a=rtcp-fb:(\d+)').firstMatch(line);

      if (rtpmapMatch != null) {
        final payload = rtpmapMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      } else if (fmtpMatch != null) {
        final payload = fmtpMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      } else if (rtcpFbMatch != null) {
        final payload = rtcpFbMatch.group(1)!;
        if (!allowedPayloads.contains(payload)) {
          shouldKeepLine = false;
        }
      }

      if (shouldKeepLine) {
        filteredLines.add(line);
      }
    }

    final modifiedSdp = filteredLines.join('\r\n');
    return modifiedSdp;
  }

  Future<void> decline() async {
    try {
      await _janusSipPlugin.decline();
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to decline call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> transfer(String extNum, String sipProxy, {String? replaceCallId}) async {
    try {
      await _janusSipPlugin.transfer(buildSipUri(extNum, sipProxy), replace: replaceCallId);
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to transfer call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> hold() async {
    try {
      await _janusSipPlugin.hold(SipHoldState.INACTIVE);
      _updateSipCallState(onhold: true);
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to hold call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> unhold() async {
    try {
      await _janusSipPlugin.unhold();
      _updateSipCallState(onhold: false);
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to unhold call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  Future<void> toggleHold() async {
    if (currSipCallState.onhold) {
      await unhold();
    } else {
      await hold();
    }
  }

  Future<void> hangup() async {
    if (currSipCallState.status.value >= SipPluginStatus.hangingup.value) {
      log.w('SIP plugin handleId: $handleId - hanging up. Skipped hangupCall');
      return;
    }
    try {
      _updateSipPluginStatus(SipPluginStatus.hangingup);
      await _janusSipPlugin.hangup();
    } catch (e) {
      log.e('SIP plugin handleId: $handleId - Failed to hangup call', error: e);
      _updateSipPluginStatus(SipPluginStatus.error);
    }
  }

  /// Disable the sender tracks. Opposite device won't receive any sound.
  Future<void> muteMicAudio(bool mute) async {
    log.t('muteMicAudio - mute:$mute');
    var senders = await _janusSipPlugin.webRTCHandle?.peerConnection?.senders;
    senders?.forEach((element) {
      if (element.track?.kind == 'audio') {
        element.track?.enabled = !mute;
      }
    });
    _updateSipCallState(localAudioDisabled: mute);
  }

  /// Disable the receiver tracks. Current device won't receive any sound.
  Future<void> muteSpeakerAudio(bool mute) async {
    log.t('muteSpeakerAudio - mute:$mute');
    var receivers = await _janusSipPlugin.webRTCHandle?.peerConnection?.receivers;
    receivers?.forEach((element) {
      if (element.track?.kind == 'audio') {
        element.track?.enabled = !mute;
      }
    });
    _updateSipCallState(remoteAudioDisabled: mute);
  }

  Future<void> sendDTMF(String tone) async {
    try {
      RTCPeerConnection? peerConnection = _janusSipPlugin.webRTCHandle!.peerConnection;
      if (peerConnection == null) {
        throw Exception('Peer connection not found');
      }
      List<RTCRtpSender> senders = await peerConnection.senders;
      RTCRtpSender audioSender = senders.firstWhere(
        (sender) => sender.track?.kind == 'audio',
        orElse: () => throw Exception('No audio sender found'),
      );
      RTCDTMFSender dtmfSender = audioSender.dtmfSender;
      if (await dtmfSender.canInsertDtmf()) {
        await dtmfSender.insertDTMF(tone);
        log.t('DTMF $tone sent');
      } else {
        log.w('Cannot insert DTMF. Sender is not ready.');
      }
    } catch (e) {
      log.e('Failed to send DTMF', error: e);
    }
  }

  void recordTransferData(SipTransferModel transferModel) {
    _updateSipCallState(transferData: transferModel);
  }

  void clearTransferData() {
    // cannot use _updateSipCallState because it doesn't update the field if we pass in null
    // so explicitly create new state to clear transferData
    SipCallState newSipCallState = SipCallState(
      handleId: currSipCallState.handleId,
      isHelper: currSipCallState.isHelper,
      status: currSipCallState.status,
      callId: currSipCallState.callId,
      counterPartUri: currSipCallState.counterPartUri,
      counterPartName: currSipCallState.counterPartName,
      startTimestamp: currSipCallState.startTimestamp,
      onhold: currSipCallState.onhold,
      localAudioDisabled: currSipCallState.localAudioDisabled,
      remoteAudioDisabled: currSipCallState.remoteAudioDisabled,
      transferData: null,
    );
    if (currSipCallState != newSipCallState) {
      _prevSipCallState = currSipCallState;
    }
    _sipCallStateController.add(newSipCallState);
  }

  static String buildSipUri(String extNum, String sipProxy) {
    const prefix = 'sip:';
    if (sipProxy.startsWith(prefix)) {
      sipProxy = sipProxy.substring(prefix.length).trim();
    }
    return 'sip:${extNum.replaceAll(' ', '').trim()}@$sipProxy';
  }

  static (String extNum, String sipProxy) parseSipUri(String uri) {
    const prefix = 'sip:';

    // Validate and remove the 'sip:' prefix
    if (!uri.startsWith(prefix)) {
      log.e('URI does not start with $prefix');
      return ('', '');
    }

    // Get the part of the string after 'sip:'
    final content = uri.substring(prefix.length).trim();

    // Split the content by the '@' symbol
    final parts = content.split('@');

    // Check if we have exactly two parts (extNum and sipProxy)
    if (parts.length != 2) {
      log.e('URI content is malformed or missing the "@" separator: $content');
      return ('', '');
    }

    final extNum = parts[0];
    final sipProxy = parts[1];

    return (extNum, sipProxy);
  }
}
