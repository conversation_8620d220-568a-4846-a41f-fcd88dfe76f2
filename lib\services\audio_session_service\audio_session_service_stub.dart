import 'package:ddone/services/audio_session_service/audio_session_service.dart';
import 'package:ddone/utils/logger_util.dart';

class AudioSessionServiceStub implements AudioSessionService {
  @override
  bool get interrupted => false;

  @override
  Future<void> resetSession() async {
    log.t('Stubbing resetSession on this platform');
  }

  @override
  Future<void> activateRingtoneSession() async {
    log.t('Stubbing activateRingtoneSession on this platform');
  }

  @override
  Future<void> activateCallSession() async {
    log.t('Stubbing activateCallSession on this platform');
  }
}
