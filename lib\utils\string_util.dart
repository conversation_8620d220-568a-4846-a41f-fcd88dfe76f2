import 'package:flutter/foundation.dart';

class StringUtil {
  /// Converts any Dart value into a human-readable string representation with proper formatting.
  ///
  /// This function improves readability by adding line breaks and indentation to collections
  /// and nested structures, making it ideal for debugging and inspection purposes.
  ///
  /// ## Features
  /// * Handles primitive types (String, num, bool, DateTime)
  /// * Formats collections (List, Set, Map) with line breaks and indentation
  /// * Processes nested structures recursively with proper indentation levels
  /// * Attempts to improve readability of custom objects
  ///
  /// ## Parameters
  /// * [value]: The Dart value to be converted to a formatted string (any type)
  /// * [indent]: Optional indentation level for nested structures (default: 0)
  ///
  /// ## Returns
  /// A formatted string representation of the provided value.
  ///
  /// ## Examples
  ///
  /// ### Simple List
  /// ```dart
  /// List<String> fruits = ['apple', 'banana', 'cherry'];
  /// print(prettyPrint(fruits));
  /// ```
  /// Output:
  /// ```
  /// [
  ///   "apple",
  ///   "banana",
  ///   "cherry"
  /// ]
  /// ```
  ///
  /// ### Nested Structure
  /// ```dart
  /// var person = {
  ///   'name': 'John',
  ///   'address': {'city': 'New York'},
  ///   'hobbies': ['reading', 'coding']
  /// };
  /// print(prettyPrint(person));
  /// ```
  /// Output:
  /// ```
  /// {
  ///   "name": "John",
  ///   "address": {
  ///     "city": "New York"
  ///   },
  ///   "hobbies": [
  ///     "reading",
  ///     "coding"
  ///   ]
  /// }
  /// ```
  static String prettyPrint(dynamic value, {int indent = 0}) {
    // Return the string representation directly for null or simple types
    if (value == null || value is num || value is bool || value is String || value is DateTime) {
      if (value is String) {
        return '"$value"';
      }
      return value.toString();
    }

    String indentation = ' ' * indent;
    String nextIndentation = ' ' * (indent + 2);
    StringBuffer buffer = StringBuffer();

    // Handle collections (List, Set, Map)
    if (value is List) {
      if (value.isEmpty) return '[]';

      buffer.write('[\n');
      for (int i = 0; i < value.length; i++) {
        buffer.write('$nextIndentation${prettyPrint(value[i], indent: indent + 2)}');
        if (i < value.length - 1) {
          buffer.write(',\n');
        } else {
          buffer.write('\n');
        }
      }
      buffer.write('$indentation]');
      return buffer.toString();
    } else if (value is Set) {
      if (value.isEmpty) return '{}';

      buffer.write('{\n');
      int i = 0;
      for (var item in value) {
        buffer.write('$nextIndentation${prettyPrint(item, indent: indent + 2)}');
        if (i < value.length - 1) {
          buffer.write(',\n');
        } else {
          buffer.write('\n');
        }
        i++;
      }
      buffer.write('$indentation}');
      return buffer.toString();
    } else if (value is Map) {
      if (value.isEmpty) return '{}';

      buffer.write('{\n');
      int i = 0;
      value.forEach((key, val) {
        buffer
            .write('$nextIndentation${prettyPrint(key, indent: indent + 2)}: ${prettyPrint(val, indent: indent + 2)}');
        if (i < value.length - 1) {
          buffer.write(',\n');
        } else {
          buffer.write('\n');
        }
        i++;
      });
      buffer.write('$indentation}');
      return buffer.toString();
    }

    // For other objects, try to handle them by using their toString
    // but add proper indentation
    String stringRepresentation = value.toString();

    // Check if the toString implementation returns something that looks like a structured object
    if (stringRepresentation.startsWith('{') && stringRepresentation.endsWith('}')) {
      // Attempt to make it more readable by adding newlines
      stringRepresentation = stringRepresentation
          .replaceAll(', ', ',\n$nextIndentation')
          .replaceFirst('{', '{\n$nextIndentation')
          .replaceFirst('}', '\n$indentation}');
    }

    return stringRepresentation;
  }

  /// Clips a long string to a specified character count and appends '...'
  /// if the string is longer than the count.
  ///
  /// [text]: The input string to be clipped.
  /// [maxLength]: The maximum number of characters to keep.
  ///
  /// Returns the clipped string with '...' appended if necessary,
  /// or the original string if it's shorter than or equal to [maxLength].
  static String clipString(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    } else {
      return '${text.substring(0, maxLength)}...';
    }
  }

  /// Redacts a sensitive string by showing only the first few characters.
  ///
  /// This function is designed to prevent sensitive information from being
  /// displayed in logs, UI, or error reports, particularly in production
  /// environments.
  ///
  /// The redaction behavior is controlled by the build mode:
  /// - In **debug** and **profile** builds, the original string is returned
  ///   to facilitate debugging.
  /// - In **release** builds, the string is redacted.
  ///
  /// The redaction logic displays the first [visible] characters of the string
  /// followed by an ellipsis and the total length of the original string.
  ///
  /// If the string is null or empty, it returns the string 'null'. If the string's
  /// length is less than or equal to [visible], the entire string is replaced
  /// with asterisks.
  ///
  /// Example:
  /// ```
  /// // Assuming kReleaseMode is true
  /// String card = '1234567890123456';
  /// String redactedCard = redact(card); // Returns '1234…(len=16)'
  ///
  /// String shortString = '123';
  /// String redactedShort = redact(shortString, visible: 4); // Returns '***'
  ///
  /// String? nullString;
  /// String redactedNull = redact(nullString); // Returns 'null'
  /// ```
  ///
  /// @param value The string to be redacted. Can be null.
  /// @param visible The number of initial characters to show. Defaults to 4.
  /// @returns The redacted string in release mode, or the original string
  ///          in debug/profile mode.
  static String redact(String? value, {int visible = 4}) {
    if (value == null || value.isEmpty) return 'null';
    if (!kReleaseMode) return value;
    if (value.length <= visible) return '*' * value.length;
    return '${value.substring(0, visible)}…(len=${value.length})';
  }
}
