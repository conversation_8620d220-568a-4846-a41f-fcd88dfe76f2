import 'dart:async';

import 'package:ddone/environments/env.dart';
import 'package:ddone/models/enums/dialpad_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_session_service/audio_session_service.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:logger/logger.dart';
import 'package:media_kit/media_kit.dart';

class MediaService {
  Logger log = sl.get<Logger>();
  AudioSessionService audioSessionService = sl.get<AudioSessionService>();

  Player? _ringtonePlayer;
  late Map<DialpadEnum, Player> _dialpadPlayers;
  Completer<void> _ringtoneReadyCompleter = Completer<void>();
  Completer<void> _dailpadReadyCompleter = Completer<void>();

  bool _isRingtonePlaying = false;

  MediaService() {
    _ringtonePlayer = Player();
    _dialpadPlayers = {for (var dialpad in DialpadEnum.values) dialpad: Player()};
    _initializePlayers();
  }

  Future<void> _initializePlayers() async {
    try {
      List<Future<void>> awaitInitialize = [
        _initializeRingtonePlayer(),
      ];
      for (var dialpad in DialpadEnum.values) {
        awaitInitialize.add(_initializeDialpadPlayer(dialpad));
      }
      await Future.wait(awaitInitialize);
      log.t('Ringtone and dialpad tone ready');
    } catch (e) {
      log.e('Error initializing media players', error: e);
      if (!_ringtoneReadyCompleter.isCompleted) _ringtoneReadyCompleter.completeError(e);
      if (!_dailpadReadyCompleter.isCompleted) _dailpadReadyCompleter.completeError(e);
    }
  }

  Future<void> _initializeRingtonePlayer() async {
    await _ringtonePlayer!.open(
      Media('asset://assets/${env!.incomingCallAudioSource}'),
      play: false,
    );
    await _ringtonePlayer!.setPlaylistMode(PlaylistMode.loop);
    await _ringtonePlayer!.setVolume(100);
    if (!_ringtoneReadyCompleter.isCompleted) _ringtoneReadyCompleter.complete();
  }

  Future<void> _resetRingtonePlayer() async {
    log.t('reset ringtone player');
    await _disposeRingtonePlayer();
    _ringtonePlayer = Player();
    await _initializeRingtonePlayer();
  }

  Future<void> _initializeDialpadPlayer(DialpadEnum dialpad) async {
    await _dialpadPlayers[dialpad]!.open(
      Media('asset://assets/${dialpad.dialpadTonePath()}'),
      play: false,
    );
    await _dialpadPlayers[dialpad]!.setVolume(50);
    if (!_dailpadReadyCompleter.isCompleted) _dailpadReadyCompleter.complete();
  }

  Future<void> playRingtone() async {
    if (isMobile) return; // in mobile we don't need to ring because callkit will handle ringing
    if (_isRingtonePlaying) return;
    await _ringtoneReadyCompleter.future;
    log.t('Play ringtone');
    try {
      _isRingtonePlaying = true;
      await audioSessionService.activateRingtoneSession();
      await _ringtonePlayer!.seek(Duration.zero);
      await _ringtonePlayer!.play();
      log.t('Played ringtone');
    } catch (e) {
      log.e('Error playing ringtone', error: e);
      await audioSessionService.resetSession();
      _isRingtonePlaying = false;
    }
  }

  Future<void> stopRingtone() async {
    if (isMobile) return;
    if (!_isRingtonePlaying) return;
    await _ringtoneReadyCompleter.future;
    log.t('Stop ringtone');
    try {
      _isRingtonePlaying = false;
      await _ringtonePlayer!.stop();
      log.t('Stopped ringtone');
    } catch (e) {
      log.e('Error stopping ringtone', error: e);
      _isRingtonePlaying = true;
    } finally {
      // Need to reset because after a call PeerConnection seems like will tear down everything related to media,
      // so subsequent ringtone won't work after a call if we don't reset.
      await _resetRingtonePlayer();
      // ios we need some delay for player to fully stop the media, otherwise will hit error when deactivating audiio session
      await Future.delayed(const Duration(milliseconds: 100));
      await audioSessionService.resetSession();
    }
  }

  Future<void> playDialpadTone(DialpadEnum dialpad) async {
    await _dailpadReadyCompleter.future;
    log.t('Play dialpad tone for $dialpad');
    try {
      Player dialpadPlayer = _dialpadPlayers[dialpad]!;
      await dialpadPlayer.seek(Duration.zero);
      await dialpadPlayer.play();
      await Future.delayed(const Duration(milliseconds: 100));
      await dialpadPlayer.stop();
    } catch (e) {
      log.e('Error playing dialpad tone for $dialpad', error: e);
    }
  }

  // IMPORTANT: call this when service is no longer needed (in cubit's close method)
  Future<void> dispose() async {
    try {
      await Future.wait([
        _disposeRingtonePlayer(),
        _disposeDialpadPlayers(),
      ]);
    } catch (e) {
      log.e('Error disposing media', error: e);
    }
    _dialpadPlayers.clear();
  }

  Future<void> _disposeRingtonePlayer() async {
    await _ringtonePlayer?.dispose();
    _ringtonePlayer = null;
    _ringtoneReadyCompleter = Completer<void>();
  }

  Future<void> _disposeDialpadPlayers() async {
    List<Future<void>> awaitDisposes = [];
    for (var dailpadPlayer in _dialpadPlayers.values) {
      awaitDisposes.add(dailpadPlayer.dispose());
    }
    await Future.wait(awaitDisposes);
    _dialpadPlayers.clear();
    _dailpadReadyCompleter = Completer<void>();
  }
}
