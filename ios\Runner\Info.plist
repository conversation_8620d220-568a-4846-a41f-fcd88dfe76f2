<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true />
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>$(BUNDLE_DISPLAY_NAME)</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(BUNDLE_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(CURRENT_PROJECT_VERSION)</string>
    <key>FirebaseMessagingAutoInitEnabled</key>
    <false />
    <key>ITSAppUsesNonExemptEncryption</key>
    <false />
    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>itms-apps</string>
      <string>https</string>
    </array>
    <key>LSRequiresIPhoneOS</key>
    <true />
    <key>LSSupportsOpeningDocumentsInPlace</key>
    <true />
    <key>NSAppleMusicUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to your Apple Music library to provide a better music
      experience.</string>
    <key>NSCameraUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to the camera to scan QR codes.</string>
    <key>NSContactsUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to your contacts so you can call friends from the app.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to the microphone to make calls.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to your photo library to let you send images in chat.</string>
    <key>PermissionGroupNotification</key>
    <string>$(PRODUCT_NAME) would like to send you notifications.</string>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true />
    <key>UIBackgroundModes</key>
    <array>
      <string>fetch</string>
      <string>remote-notification</string>
      <string>voip</string>
    </array>
    <key>UIFileSharingEnabled</key>
    <true />
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false />
  </dict>
</plist>