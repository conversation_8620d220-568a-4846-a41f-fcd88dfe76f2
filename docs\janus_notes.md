# Core Concepts for High-Performance SIP Call Handling

This document provides a comprehensive guide to building a high-performance SIP call-handling architecture using the `flutter_janus_client` package. The key to this approach is leveraging the **Master & Helper Plugin Architecture** with a **standby helper pool** to minimize latency and ensure robust handling of multiple concurrent calls.

---

### 1. Handling Multiple Calls

The most efficient architecture for handling simultaneous or sequential calls is to use a single `JanusClient` and `JanusSession` with **multiple `JanusSipPlugin` instances**. This method is efficient because it utilizes a single, persistent connection (WebSocket or REST polling) to the Janus server. Creating a new `JanusClient` for each call would introduce unnecessary overhead.

---

### 2. Plugin Lifecycle: `attach()`, `dispose()`, and Reusability

* **Creation**: A new plugin is created and activated by calling `session.attach<JanusSipPlugin>()`.
* **Destruction**: A plugin is destroyed by calling `plugin.dispose()`. This action cleans up all client-side resources, including the `RTCPeerConnection` object and media streams. The corresponding server-side handle is automatically garbage-collected.
* **Reusability**: A key rule is that a plugin instance **cannot be reused** after `plugin.dispose()` has been called. For each new call, you must `attach()` a new plugin instance.

---

### 3. WebRTC Fundamentals

Understanding the core WebRTC functions is crucial:

* `initializeWebRTCStack()`: This internal method builds the `RTCPeerConnection` object. **You should never call this manually**; it is automatically invoked when you `attach()` a plugin. 
* `initializeMediaDevices()`: This helper function accesses the microphone and camera. For multi-call scenarios, it's better to manage media yourself:
    1.  Call `navigator.mediaDevices.getUserMedia()` once to get a local audio track.
    2.  Manually add this single track to each plugin's `peerConnection` using `peerConnection.addTrack()`.
* `handleRemoteJsep()`: This is the "agreement" step where you process the remote party's call plan (JSEP). You **must call this** whenever you receive a `jsep` in an event, such as `SipIncomingCallEvent`, `SipAcceptedEvent`, or `SipProgressEvent`.

---

### 4. The Master & Helper Plugin Architecture

This is the standard pattern for managing multiple concurrent calls with a single SIP registration.

#### **Master Plugin Lifecycle (Persistent)**

The master plugin acts as your SIP identity and should be persistent for as long as the user is "logged in".

1.  **On App Start**: `attach()` and `register()` once. Keep this instance.
2.  **During a Call**: Use this instance to place the **first** outgoing call or to receive the **first** incoming call.
3.  **After a Call**: Simply `hangup()`. **Do not** `dispose()`.
4.  **On App Exit**: Call `unregister()` and then `dispose()`.

#### **Helper Plugin Lifecycle (Disposable)**

A helper plugin is a temporary tool designed to handle a single concurrent call.

1.  **Creation**: Create on-demand when a new call needs to be handled.
2.  **Registration**: Call `register()` on the helper with `type: 'helper'` and `masterId: masterPlugin.handleId`. This links it to the master's registration without performing a new SIP registration.
3.  **After its Call**: Call `hangup()` and then `dispose()` the instance completely.

#### **The "Receptionist" Event Flow**

* A `SipIncomingCallEvent` is **only ever delivered to the master plugin**.
* The master plugin's job is to act as a "receptionist":
    1.  Receive the `SipIncomingCallEvent`.
    2.  Create a new `helperPlugin` on-demand.
    3.  "Hand off" the call by passing the `jsep` from the event to the helper using `helperPlugin.handleRemoteJsep(event.jsep)`.
    4.  The helper then takes over management of that specific call.

---

### 5. Technical Design: Standby Helper Pool

This advanced architecture maintains a "hot" standby helper to eliminate latency for incoming and outgoing calls. It's a form of an **Object Pool with a pool size of one**.

#### **Core Components**

* **Master Plugin**: Persistent, acts as the receptionist, and manages the SIP online/offline status. **Never handles a media call itself in this architecture**.
* **Active Helper Plugin**: Transient, manages a single active call from start to finish.
* **Standby Helper Plugin**: Semi-persistent, created in advance and waits to be used. Once used, it becomes an active helper, and a new standby is immediately provisioned.

#### **Detailed Workflow**

1.  **Application Initialization**:
    * Create a single `JanusClient` and `JanusSession`.
    * Instantiate the **Master Plugin**.
    * Instantiate the **First Standby Helper** by defining and calling a `createNewStandbyHelper()` function that handles the `attach()` and `register()` process for a helper.

2.  **Handling an Incoming Call**:
    * The `masterPlugin` receives a `SipIncomingCallEvent`.
    * **Activate the Standby Helper**: The current `standbyHelper` is retrieved and becomes the `activeHelper`.
    * **Replenish the Pool**: Immediately call `createNewStandbyHelper()` to prepare for the next call. This is a critical step for back-to-back calls.
    * **Hand off the Call**: Pass the `jsep` from the event to the `activeHelper` using `activeHelper.handleRemoteJsep(jsep)`.
    * Manage the call with the `activeHelper`, and upon termination (e.g., `SipHangupEvent`), call `activeHelper.dispose()`.

3.  **Initiating an Outgoing Call**:
    * The user initiates a call.
    * **Activate the Standby Helper**: The logic is identical to the incoming call flow.
    * **Replenish the Pool**: Immediately call `createNewStandbyHelper()`.
    * Manage the call with the `activeHelper`, and upon termination, call `activeHelper.dispose()`.

#### **Benefits of this Architecture**

* **High Performance**: Call setup latency is minimized by having a pre-attached and pre-registered helper ready at all times.
* **Scalability**: This pattern scales cleanly to support N concurrent calls by managing a list of active helpers.
* **Robustness**: It provides a clear separation of concerns, making the code easier to maintain and debug. The master's role is isolated from the complexities of active call management.