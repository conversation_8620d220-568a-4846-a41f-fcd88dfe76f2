An **Attended Call Transfer** (also known as a **consultative** or **warm transfer**) in SIP is a process where the person initiating the transfer first speaks to the destination party to announce the transfer and confirm they can take the call before connecting the original caller.

The SIP protocol uses the **REFER** method, often in conjunction with the **Replaces** header, to accomplish this.

Here is a detailed breakdown from the perspective of the three involved parties:

| Role | SIP Terminology | Initial Parties |
| :--- | :--- | :--- |
| **Original Caller** | **Transferee** (Party A) | Party A |
| **Transferring Party** | **Transferor** (Party B) | Party B |
| **Final Recipient** | **Transfer Target** (Party C) | Party C |

---

## 1. Transferring Party (Transferor - Party B)

The Transferor (Party B) is the agent or user who decides the call needs to be moved. Their actions drive the entire process.

### Phase 1: Call Setup and Consultation
1.  **Original Call Active:** Party B is active in a call with Party A (the Transferee).
2.  **Place Party A on Hold:** Party B initiates a **Hold** operation, typically by sending a SIP **re-INVITE** to Party A with media attributes indicating that Party A should stop sending/receiving media (e.g., setting the connection address to `0.0.0.0` or using the `sendonly/recvonly` attributes). Party A is now listening to music on hold or silence.
3.  **Establish Consultation Call:** Party B initiates a **new INVITE** to Party C (the Transfer Target) to start a second, private call (the "consultation call").
4.  **Consultation:** Party C answers, and Party B speaks to them, explaining the situation and announcing that Party A will be transferred.
5.  **Preparation for Transfer:** Once Party C agrees to the transfer, Party B is ready to connect Party A and Party C.

### Phase 2: Transfer Execution
6.  **Initiate Transfer (REFER):** Party B sends a SIP **REFER** request to **Party A** (the Transferee). This message essentially tells Party A: "Instead of talking to me, you should establish a new call with Party C."
    * The `Refer-To` header in the REFER request contains the contact information (SIP URI) for Party C.
    * Crucially for an *attended* transfer, the `Refer-To` header also contains the **Replaces** header information for the active consultation call between Party B and Party C (Call-ID, To-Tag, From-Tag). This tells Party A's device to instruct Party C's device to **replace** the consultation call with the new call from Party A.
7.  **Receive Acknowledgment:** Party B receives a `202 Accepted` response from Party A, confirming that the transfer request was received and accepted.
8.  **Receive Status Updates (NOTIFY):** Party B receives one or more SIP **NOTIFY** messages from Party A, which report the progress of Party A's attempt to contact Party C (e.g., "100 Trying," "200 OK").
9.  **Clear Calls (BYE):** Once Party B receives a final, successful **NOTIFY** (e.g., "200 OK"), indicating that Party A and Party C are connected, Party B sends a **BYE** to both Party A and Party C's devices to tear down its now-redundant call legs. Party B is now disconnected from the conversation.

---

## 2. Original Caller (Transferee - Party A)

The Transferee (Party A) is the party whose call is being transferred. They are generally passive in the SIP signaling until they receive the REFER request.

### Phase 1: On Hold
1.  **Call Active with B:** Party A is speaking with Party B.
2.  **Placed on Hold:** Party A receives a **re-INVITE** from Party B and places the call on hold. Party A is waiting for Party B to return to the line.

### Phase 2: Executing the Transfer
3.  **Receive Transfer Request (REFER):** Party A receives the **REFER** request from Party B, which instructs them to call Party C.
4.  **Acknowledge REFER:** Party A sends a `202 Accepted` response back to Party B.
5.  **Initiate New Call:** Party A immediately sends a new SIP **INVITE** to Party C (the address taken from the `Refer-To` header).
    * This **INVITE** includes the **Replaces** header information (which was escaped inside the `Refer-To` from Party B). This is the key that tells Party C's device that this new call from Party A should replace the active consultation call with Party B.
6.  **Call Established:** Party A and Party C complete the SIP call setup (exchange 100, 180, 200 OK, ACK).
7.  **Notify Status:** As the new call is set up, Party A sends **NOTIFY** messages to Party B to inform the Transferor of the progress and ultimate success (or failure) of the new call.
8.  **Media Flow:** Voice media (RTP) now flows directly between Party A and Party C.
9.  **Original Call Ends:** Party A eventually receives a **BYE** from Party B, ending the original call leg.

---

## 3. Final Recipient (Transfer Target - Party C)

The Transfer Target (Party C) is the final destination for the call. They are initially consulted and then connected to the original caller.

### Phase 1: Consultation
1.  **Receive Consultation Call:** Party C receives a standard **INVITE** from Party B (the Transferor).
2.  **Consultation:** Party C answers, and has a private conversation with Party B, being briefed on the nature of the incoming transfer.
3.  **Ready to Receive:** Party C's device remains active on the consultation call with Party B.

### Phase 2: Call Replacement
4.  **Receive Replacement Call (INVITE with Replaces):** Party C receives a new **INVITE** request, this time from Party A (the Transferee).
    * This INVITE contains the special **Replaces** header that matches the details of the active consultation call with Party B.
5.  **Replace Session:** Party C's device recognizes the **Replaces** header, understands that this new call from Party A is meant to replace the active call with Party B, and accepts the new **INVITE**.
6.  **Terminate Old Call:** Party C's device sends a **BYE** to Party B to terminate the consultation call.
7.  **Final Call Established:** Party C and Party A complete the call setup. Party C is now speaking directly to the Original Caller (Party A). Party C might be aware the call was transferred, but the active SIP session is now a direct A-to-C connection.



--------


✦ Of course. This is an excellent question that gets to the heart of advanced telephony features in
  SIP. An attended transfer is a multi-step process, and understanding the flow of calls and events
  is crucial.

  First, I will analyze the relevant source code to give you a precise and grounded explanation
  based on this project's implementation. I'll need to examine the SIP plugin and the data
  structures it uses.

 ╭──────────────────────────────────────────────────────────────────────────────────────────╮
 │ ✓  ReadManyFiles Will attempt to read and concatenate files using patterns:…             │
 │                                                                                          │
 │                                                                                          │
 │    ### ReadManyFiles Result (Target Dir:                                                 │
 │    `/mnt/c/Users/<USER>/Project/flutter_janus_client`)                                     │
 │                                                                                          │
 │    Successfully read and concatenated content from **2 file(s)**.                        │
 │                                                                                          │
 │    **Processed Files:**                                                                  │
 │    - `lib/interfaces/sip/sip_event.dart`                                                 │
 │    - `lib/wrapper_plugins/janus_sip_plugin.dart`                                         │
 ╰──────────────────────────────────────────────────────────────────────────────────────────╯
✦ Excellent. After analyzing the janus_sip_plugin.dart and sip_event.dart files, I can provide a
  detailed, context-aware explanation of the attended transfer flow.

  1. Explanation of the replace Argument

  The description you found is key: "[replace]: call-ID of the call this attended transfer is
  supposed to replace".

  In an attended transfer, you (let's call you Alice) are in an active call with one person (Bob).
  You want to transfer Bob to a third person (Carol). Before completing the transfer, you first
  call Carol to ensure she is available and willing to take the call. This means for a brief
  period, you have two separate calls active:

   1. Call 1: Alice ↔ Bob (this call is usually put on hold)
   2. Call 2: Alice ↔ Carol

  The replace argument in transfer(uri: carol_uri, replace: call_id_2) tells the Janus server:
  "Transfer the person I'm talking to in my other call (Bob) to the URI provided (carol_uri), and
  do so by replacing my current call with Carol (Call 2)".

  So, the call-ID you must use for the replace argument is the `call_id` of the second call you
  initiated (Alice to Carol). Janus then effectively "stitches" Bob and Carol together and hangs up
   both of your connections to them.

  If you omit the replace argument, you are performing a blind transfer. You are telling Janus, "I
  don't care if Carol answers, just send Bob to her URI," and your call with Bob ends immediately.

  2. Detailed Attended Transfer Flow

  Here is the step-by-step flow, including methods to call and events to listen for, based on the
  JanusSipPlugin implementation.

  Actors:
   * You (Alice): The user of your Flutter app.
   * Bob: The person Alice is initially talking to.
   * Carol: The person Alice wants to transfer Bob to.

  Assumptions:
   * You have a JanusSipPlugin instance named sipPlugin.
   * You are listening to the sipPlugin.typedMessages stream to receive strongly-typed events like
     SipAcceptedEvent, SipHangupEvent, etc.
   * Alice is already in an active call with Bob. Your app has received a SipAcceptedEvent for this
     call and has stored its call_id. Let's call it call_id_bob.

  ---

  Step 1: Alice Puts Bob on Hold

  To talk to Carol, Alice first needs to put her active call with Bob on hold.

   * Action: Call the hold() method. You should specify a direction to indicate you will not be
     sending audio anymore.
   * Method:

   1     await sipPlugin.hold(SipHoldState.SENDONLY);
   * Event to Listen For: Janus will send a SIP re-INVITE to the peer. While there isn't a specific
     "hold succeeded" event, the operation is considered successful if the Future completes without a
      JanusError. Your UI should now reflect that Bob is on hold.

  ---

  Step 2: Alice Initiates a New Call to Carol

  Now, Alice opens a new line to call Carol. This requires a second JanusSipPlugin instance, as
  each plugin handle in Janus typically manages a single call peer-to-peer session.

   * Action: Create a new JanusSipPlugin and use it to call Carol's SIP URI.
   * Method:

   1     // You need a second plugin instance for the second call
   2     JanusSipPlugin sipPlugin2 = await janusClient.attach<JanusSipPlugin>();
   3
   4     // Make the call to Carol
   5     await sipPlugin2.call('sip:<EMAIL>');
   * Events to Listen For (on `sipPlugin2.typedMessages`):
       1. SipProceedingEvent or SipRingingEvent: Indicates that the call is being set up and Carol's
          phone is ringing.
       2. SipAcceptedEvent: Carol has answered the call. Crucially, you must get the `call_id` from
          this event.

   1         String call_id_carol;
   2         sipPlugin2.typedMessages.listen((event) {
   3           if (event.event.plugindata?.data is SipAcceptedEvent) {
   4             var acceptedEvent = event.event.plugindata!.data as SipAcceptedEvent;
   5             // Store the call_id of the call with Carol
   6             call_id_carol = acceptedEvent.result!.callId!;
   7             // Now Alice can talk to Carol
   8           }
   9         });

  ---

  Step 3: Alice Talks to Carol

  Alice now has an active call with Carol and a held call with Bob. She confirms that Carol is
  ready to be transferred.

  ---

  Step 4: Alice Initiates the Transfer

  This is the final step. Alice uses her original plugin (the one connected to Bob) to execute the
  transfer.

   * Action: Call the transfer() method on the plugin instance that is managing the call with Bob.
   * Method:

   1     // Use the FIRST plugin (connected to Bob)
   2     // Provide Carol's URI and the call_id of the SECOND call (Alice to Carol)
   3     await sipPlugin.transfer(
   4       uri: 'sip:<EMAIL>',
   5       replace: call_id_carol
   6     );
   * Events to Listen For:
       * On sipPlugin.typedMessages (Bob's call): You will receive a SipHangupEvent. The reason will
         typically indicate a successful transfer (e.g., "Call completed elsewhere"). This signals
         that your connection to Bob has been terminated because he is now being connected to Carol.
       * On sipPlugin2.typedMessages (Carol's call): You will also receive a SipHangupEvent. This is
         because your connection to Carol has also been terminated as she is now connected to Bob.

  When you receive hangup events for both of your calls, the attended transfer is complete.

  ---

  3. Handling Failed Transfers

  Scenario A: Carol Does Not Answer or Declines the Call

   * Detection: In Step 2, instead of a SipAcceptedEvent, you will receive a SipHangupEvent on
     sipPlugin2.typedMessages. The reason in the event will indicate why the call failed (e.g., "Not
     Found", "Busy Here", "Decline").
   * Recovery:
       1. Your app should notify Alice that the transfer target (Carol) is unavailable.
       2. Clean up the second plugin instance: await sipPlugin2.dispose();.
       3. Provide a UI option for Alice to "Resume Call" with Bob.
       4. When Alice chooses to resume, call unhold() on the original plugin:

   1         await sipPlugin.unhold();
       5. Alice is now back in an active call with Bob.

  Scenario B: The `transfer()` Command Itself Fails

   * Detection: The Future returned by sipPlugin.transfer() will throw a JanusError. The error
     message from Janus might contain more details. This is a rarer scenario, possibly due to a
     server-side issue or misconfiguration.
   * Recovery:
       1. The await will fail, and your code will jump to the catch block.
       2. Inform Alice that the transfer failed.
       3. At this point, Alice is still in two separate calls: Bob is on hold, and she has an active
          line to Carol.
       4. Your UI should reflect this state. Alice should have the option to either:
           * Manually hang up the call with Carol (await sipPlugin2.hangup();) and then un-hold the
             call with Bob (await sipPlugin.unhold();).
           * Try the transfer again.

-------

https://www.opensips.org/events/Summit-2022Athens/assets/presentations/OpenSIPS_Summit2022_Lorenzo_Miniero-janus-presentation.pdf