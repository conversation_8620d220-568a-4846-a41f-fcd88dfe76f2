import 'dart:async';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/voip_constants.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/events/firebase_event.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/events/voip_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/audio_session_service/audio_session_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/foreground_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/janus/janus_service.dart';
import 'package:ddone/services/media_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/priority_stream_manager.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/string_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:janus_client/janus_client.dart';

class VoipService with PrefsAware {
  // static final singleton pattern
  static final VoipService _instance = VoipService._internal();
  VoipService._internal()
      : _janusService = sl.get<JanusService>(),
        _callkitService = sl.get<CallkitService>(),
        _audioSessionService = sl.get<AudioSessionService>(),
        _audioDeviceService = sl.get<AudioDeviceService>(),
        _firebaseService = sl.get<FirebaseService>(),
        _hiveService = sl.get<HiveService>(),
        _mediaService = sl.get<MediaService>();
  factory VoipService() {
    return _instance;
  }

  late final JanusService _janusService;
  late final CallkitService _callkitService;
  late final AudioSessionService _audioSessionService;
  late final AudioDeviceService _audioDeviceService;
  late final FirebaseService _firebaseService;
  late final HiveService _hiveService;
  late final MediaService _mediaService;

  bool _initializing = false;
  bool _reconnecting = false;

  PriorityStreamManager<List<SipCallState>> get prioritySipCallsStateStream =>
      _janusService.prioritySipCallsStateStream;

  bool get isReady => _janusService.isReady;

  Future<bool> init({
    String? sipWsUrl,
    String? sipNumber,
    String? sipDomain,
    String? sipProxy,
    String? sipSecret,
    String? sipName,
  }) async {
    await prefs.reload();
    sipWsUrl ??= prefs.getString(CacheKeys.sipWsUrl);
    sipNumber ??= prefs.getString(CacheKeys.sipNumber);
    sipDomain ??= prefs.getString(CacheKeys.sipDomain);
    sipProxy ??= prefs.getString(CacheKeys.sipProxy);
    sipSecret ??= prefs.getString(CacheKeys.sipPwd);
    sipName ??= prefs.getString(CacheKeys.sipName);
    if ((sipWsUrl == null &&
        sipNumber == null &&
        sipDomain == null &&
        sipProxy == null &&
        sipSecret == null &&
        sipName == null)) {
      return false;
    }

    if (_initializing) return false;
    try {
      _initializing = true;
      String? token;
      if (isMobile) {
        token = await _firebaseService.getFcmtoken();
      } else {
        token = 'desktop';
      }
      token ??= prefs.getString(CacheKeys.sipHeaderToken);

      if (sipWsUrl == null || sipWsUrl.trim().isEmpty) {
        sipWsUrl = env!.webSocketUrl;
      }

      bool hasInit = await _janusService.init(
        sipWsUrl: sipWsUrl,
        sipNumber: sipNumber!,
        sipDomain: sipDomain!,
        sipProxy: sipProxy!,
        sipSecret: sipSecret!,
        sipName: sipName!,
        sipHeaderToken: token!,
      );
      if (!hasInit) return false;

      prioritySipCallsStateStream.addListener(kVoipServiceSipCallsStateStreamPriority, _onSipCallsStateData,
          onError: _onSipCallsStateError);
    } catch (e) {
      log.e('Failed in VoipService Init', error: e);
      await _janusService.dispose();
    } finally {
      _initializing = false;
    }
    return true;
  }

  void _onSipCallsStateData(List<SipCallState> sipCallStates) async {
    log.d('voip service sip calls state listener data: ${StringUtil.prettyPrint(sipCallStates)}');
    _monitorAudioDevices(sipCallStates);
    _ringForIncomingCall(sipCallStates);
  }

  void _onSipCallsStateError(dynamic error) async {
    log.e('voip service sip calls state listener error: $error');
  }

  void _monitorAudioDevices(List<SipCallState> sipCallStates) async {
    // start device monitoring when there is active call
    if (sipCallStates.any((s) => s.status.isCallActive)) {
      await _audioDeviceService.startDeviceMonitoring();
    } else {
      await _audioDeviceService.stopDeviceMonitoring();
    }
  }

  void _ringForIncomingCall(List<SipCallState> sipCallStates) async {
    if (sipCallStates.any((s) => s.status == SipPluginStatus.incomingCall)) {
      await _mediaService.playRingtone();
    } else {
      await _mediaService.stopRingtone();
    }
  }

  Future<void> dispose() async {
    await _janusService.dispose();
  }

  Future<void> maintainConnection() async {
    if (!userHasLoggedIn()) return;
    if (_reconnecting || _initializing) return;
    try {
      _reconnecting = true;
      bool janusIsConnected = await _janusService.checkJanusConnection();
      log.t('VoipService - maintainConnection - janusIsConnected:$janusIsConnected');
      if (!janusIsConnected) {
        await dispose();
        await init();
      }
    } catch (e) {
      log.e('Failed in VoipService maintainConnection', error: e);
    } finally {
      _reconnecting = false;
    }
  }

  Future<int> makeCall(String displayName, String extNum, String sipProxy) async {
    int handleId = await _janusService.makeCall(displayName, extNum, sipProxy);
    return handleId;
  }

  Future<void> hangupCall(int handleId) async {
    await _janusService.hangupCall(handleId);
  }

  Future<void> acceptCall(int handleId) async {
    await _janusService.acceptCall(handleId);
  }

  Future<void> declineCall(int handleId) async {
    await _janusService.declineCall(handleId);
  }

  Future<void> blindTransferCall(int handleId, String extNum, String sipProxy) async {
    await _janusService.blindTransferCall(handleId, extNum, sipProxy);
  }

  Future<void> initiateAttendedTransfer(int handleId, String displayName, String extNum, sipProxy) async {
    await _janusService.initiateAttendedTransfer(handleId, displayName, extNum, sipProxy);
  }

  Future<void> completeAttendedTransfer(int handleId) async {
    await _janusService.completeAttendedTransfer(handleId);
  }

  Future<void> cancelAttendedTransfer(int handleId) async {
    await _janusService.cancelAttendedTransfer(handleId);
  }

  Future<void> holdCall(int handleId) async {
    await _janusService.holdCall(handleId);
  }

  Future<void> unholdCall(int handleId) async {
    await _janusService.unholdCall(handleId);
  }

  Future<void> toggleHoldCall(int handleId) async {
    await _janusService.toggleHoldCall(handleId);
  }

  Future<void> sendDTMF(int handleId, String tone) async {
    await _janusService.sendDTMF(handleId, tone);
  }

  Future<void> muteMicAudio(int handleId, bool mute) async {
    await _janusService.muteMicAudio(handleId, mute);
  }

  Future<void> muteSpeakerAudio(int handleId, bool mute) async {
    await _janusService.muteSpeakerAudio(handleId, mute);
  }
}
