import 'package:equatable/equatable.dart';

class SipTransferModel extends Equatable {
  final String transfereeUri;
  final String transfereeName;
  final int transfereeHandleId;
  final String transferTargetUri;
  final String transferTargetName;
  final int transferTargetHandleId;

  const SipTransferModel({
    required this.transfereeUri,
    required this.transfereeName,
    required this.transfereeHandleId,
    required this.transferTargetUri,
    required this.transferTargetName,
    required this.transferTargetHandleId,
  });

  @override
  List<Object?> get props => [
        transfereeUri,
        transfereeName,
        transfereeHandleId,
        transferTargetUri,
        transferTargetName,
        transferTargetHandleId,
      ];

  SipTransferModel copyWith({
    String? transfereeUri,
    String? transfereeName,
    int? transfereeHandleId,
    String? transferTargetUri,
    String? transferTargetName,
    String? transferTargetSipProxy,
    int? transferTargetHandleId,
  }) {
    return SipTransferModel(
      transfereeUri: transfereeUri ?? this.transfereeUri,
      transfereeName: transfereeName ?? this.transfereeName,
      transfereeHandleId: transfereeHandleId ?? this.transfereeHandleId,
      transferTargetUri: transferTargetUri ?? this.transferTargetUri,
      transferTargetName: transferTargetName ?? this.transferTargetName,
      transferTargetHandleId: transferTargetHandleId ?? this.transferTargetHandleId,
    );
  }

  Map<String, dynamic> toJson() => {
        'transfereeUri': transfereeUri,
        'transfereeName': transfereeName,
        'transfereeHandleId': transfereeHandleId,
        'transferTargetUri': transferTargetUri,
        'transferTargetName': transferTargetName,
        'transferTargetHandleId': transferTargetHandleId,
      };

  factory SipTransferModel.fromJson(Map<String, dynamic> json) => SipTransferModel(
        transfereeUri: json['transfereeUri'] as String? ?? '',
        transfereeName: json['transfereeName'] as String? ?? '',
        transfereeHandleId: json['transfereeHandleId'] as int? ?? -1,
        transferTargetUri: json['transferTargetUri'] as String? ?? '',
        transferTargetName: json['transferTargetName'] as String? ?? '',
        transferTargetHandleId: json['transferTargetHandleId'] as int? ?? -1,
      );
}
