import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/services/voip_overlay_manager.dart';

class IncomingCallDialog extends StatefulWidget {
  final String? callerID, caller;
  final VoidCallback onAccept, onDecline;

  const IncomingCallDialog({
    required this.callerID,
    required this.caller,
    required this.onAccept,
    required this.onDecline,
    super.key,
  });

  @override
  State<IncomingCallDialog> createState() => _IncomingCallDialogState();
}

class _IncomingCallDialogState extends State<IncomingCallDialog> with WidgetsBindingObserver, PrefsAware {
  late VoipCubit _voipCubit;
  late HiveService _hiveService;

  void removeWhenNotCalling() {
    // if (_voipCubit.state is! VoipSipIncomingCall && mounted) {
    //   pop();
    // }
  }

  void removeWhenInCall() {
    // if (_voipCubit.state is VoipSipAccepted && mounted) {
    //   pop();
    // }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _hiveService = sl.get<HiveService>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      removeWhenNotCalling();
      removeWhenInCall();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {}
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VoipCubit, VoipState>(
      listener: (context, voipState) {
        // if (voipState is VoipSipHangup) {
        //   popUntilInitial();

        //   _hiveService.addData<CallRecords>(
        //     data: CallRecords(
        //       contactName: widget.callerID ?? 'Unknown',
        //       did: widget.caller ?? 'Unknown',
        //       duration: '0:00',
        //       type: CallType.missed,
        //       datetime: DateTime.now(),
        //     ),
        //   );
        // } else if (voipState is VoipSipAccepted) {
        //   // Close the incoming call dialog since the accepted call screen
        //   // will be handled centrally in home.dart
        //   popUntilInitial();
        // } else if (voipState is VoipSipAcceptedError) {
        //   popUntilInitial();
        // }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          return AlertDialog(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Incoming call',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: context.deviceWidth(0.03),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    final overlayManager = sl.get<VoipOverlayManager>();
                    // overlayManager.minimizeCall(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colorTheme.onPrimaryColor.withOpacity(0.1),
                    ),
                    child: Icon(
                      Icons.minimize,
                      color: colorTheme.onPrimaryColor,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            insetPadding: const EdgeInsets.all(0),
            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
            content: SizedBox(
              width: context.deviceWidth(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Divider(
                    indent: 8.0,
                    endIndent: 8.0,
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    widget.callerID ?? 'Unknown',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: context.deviceWidth(0.03),
                    ),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    widget.caller ?? 'Unknown',
                    style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      RoundShapeInkWell(
                        color: colorTheme.connectedColor,
                        contentWidget: Icon(
                          Icons.call,
                          color: colorTheme.onPrimaryColor,
                          size: context.deviceWidth(0.04),
                        ),
                        onTap: widget.onAccept,
                      ),
                      SizedBox(
                        width: context.deviceWidth(0.1),
                      ),
                      RoundShapeInkWell(
                        checkNetwork: false,
                        color: colorTheme.errorColor,
                        contentWidget: Icon(
                          Icons.close,
                          color: colorTheme.onPrimaryColor,
                          size: context.deviceWidth(0.04),
                        ),
                        onTap: () {
                          popUntilInitial();

                          widget.onDecline();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

void showIncomingCallDialog(
  BuildContext context, {
  required String? caller,
  required String? callerID,
  required VoidCallback onAccept,
  required VoidCallback onDecline,
}) {
  showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return IncomingCallDialog(
        caller: caller,
        callerID: callerID,
        onAccept: onAccept,
        onDecline: onDecline,
      );
    },
  );
}
