import 'package:avatar_glow/avatar_glow.dart';
import 'package:collection/collection.dart';
import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/components/voip_call_dialog_controller.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/screens/transfer_dialpad.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/widgets/audio_device_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class VoipCallDialog extends StatefulWidget {
  final int handleId;
  final VoipCallDialogController controller;
  const VoipCallDialog({
    super.key,
    required this.handleId,
    required this.controller,
  });

  @override
  State<VoipCallDialog> createState() => _VoipCallDialogState();
}

class _VoipCallDialogState extends State<VoipCallDialog> {
  late VoipCubit _voipCubit;
  late TextEditingController _numberController;
  late ScrollController _scrollController;
  late StopWatchTimer? _stopWatchTimer;
  int? _callStartTimestamp;
  bool _showNumpad = false;

  // Loading states for buttons
  late bool _isAcceptingCall;
  late bool _isDecliningCall;
  late bool _isHoldingCall;
  late bool _isUnholdingCall;
  late bool _isMutingMic;
  late bool _isCompletingTransfer;

  void _initializeTimer(int? startTimestamp) {
    if (startTimestamp != null) {
      // Calculate the initial elapsed time
      final now = DateTime.now().millisecondsSinceEpoch;
      final initialElapsed = now - (startTimestamp * 1000); // Convert to milliseconds

      // Create timer with preset time
      _stopWatchTimer = StopWatchTimer(
        mode: StopWatchMode.countUp,
        presetMillisecond: initialElapsed > 0 ? initialElapsed : 0,
      );

      // Start the timer immediately
      _stopWatchTimer!.onResetTimer();
      _stopWatchTimer!.onStartTimer();
    } else {
      // Create a stopped timer
      _stopWatchTimer = StopWatchTimer(
        mode: StopWatchMode.countUp,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _numberController = TextEditingController();
    _numberController.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
    _scrollController = ScrollController();
    _initializeTimer(null);

    // Initialize loading states
    _isAcceptingCall = false;
    _isDecliningCall = false;
    _isHoldingCall = false;
    _isUnholdingCall = false;
    _isMutingMic = false;
    _isCompletingTransfer = false;
  }

  @override
  void dispose() {
    _stopWatchTimer?.dispose();
    _numberController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(builder: (context, themeState) {
      final colorTheme = themeState.colorTheme;
      final textTheme = themeState.themeData.textTheme;

      return AlertDialog(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(32.0))),
        insetPadding: const EdgeInsets.fromLTRB(48, 24, 24, 40),
        backgroundColor: const Color(0xFF424242),
        content: SizedBox(
          width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // main thing,
              BlocSelector<VoipCubit, VoipState, SipCallState?>(
                selector: (state) {
                  if (state is VoipRunning) {
                    return state.activeCalls.firstWhereOrNull((s) => s.handleId == widget.handleId);
                  }
                  return null;
                },
                builder: (context, sipCallState) {
                  if (sipCallState == null) {
                    // close call dialog when the call is no longer active.
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        if (widget.controller.isHoldDialogOpen) {
                          pop();
                        }
                        if (widget.controller.isTransferDialogVisible) {
                          pop();
                        }
                        pop();
                      }
                    });
                    return const SizedBox.shrink();
                  }
                  if (_callStartTimestamp != sipCallState.startTimestamp) {
                    _callStartTimestamp = sipCallState.startTimestamp;
                    _stopWatchTimer?.dispose(); // dispose first to avoid memory leak when initialize new timer.
                    _initializeTimer(_callStartTimestamp);
                  }
                  if (sipCallState.onhold && !widget.controller.isHoldDialogOpen) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _showOnholdCallDialog();
                      }
                    });
                  }
                  return Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildAvatarSection(sipCallState),
                      _buildCallInfoSection(sipCallState, textTheme, colorTheme),
                      _buildMiddleActionButtonsSection(sipCallState, colorTheme),
                      _buildBottomActionButtonsSection(sipCallState, colorTheme),
                      if (!_showNumpad && sipCallState.transferData != null)
                        _buildTranferSection(sipCallState, colorTheme),
                    ],
                  );
                },
              ),
              // close button
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  onPressed: () {
                    if (mounted) {
                      pop();
                    }
                  },
                  icon: const Icon(
                    Icons.close,
                    color: Color(0xFFD1D0CF),
                  ),
                  iconSize: 24,
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAvatarSection(SipCallState sipCallState) {
    if (sipCallState.status == SipPluginStatus.incomingCall || _showNumpad) {
      return const SizedBox.shrink();
    }
    return AvatarGlow(
      glowColor: const Color.fromARGB(255, 109, 109, 109),
      child: Material(
        // Replace this child with your own
        elevation: 3.0,
        shape: const CircleBorder(),
        child: CircleAvatar(
          radius: context.responsiveSize<double>(
            moileSize: 70,
            tabletSize: 70,
            desktopSize: context.deviceWidth(0.03),
            largeScreenSize: context.deviceWidth(0.02),
          ),
          backgroundColor: const Color.fromARGB(255, 83, 83, 83),
          child: Icon(
            Icons.call,
            color: Colors.orange,
            size: context.responsiveSize<double>(
              moileSize: 70,
              tabletSize: 70,
              desktopSize: context.deviceWidth(0.03),
              largeScreenSize: context.deviceWidth(0.02),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCallInfoSection(SipCallState sipCallState, TextTheme textTheme, ColorTheme colorTheme) {
    List<Widget> children = [];
    children.addAll([
      Text(
        sipCallState.counterPartName,
        style: textTheme.headlineSmall!.copyWith(color: colorTheme.onBackgroundColor),
      ),
      SizedBox(height: context.deviceWidth(0.005)),
      Text(
        sipCallState.counterPartUri,
        style: textTheme.bodySmall!.copyWith(color: colorTheme.onBackgroundColor),
      ),
      SizedBox(height: context.deviceHeight(0.02)),
      Text(
        sipCallState.status.display,
        style: const TextStyle(color: Colors.orange),
      ),
      SizedBox(height: context.deviceHeight(0.02)),
    ]);
    if (sipCallState.status.value >= SipPluginStatus.accepted.value) {
      if (!_showNumpad) {
        // no place to show timer when numpad is on
        children.add(StreamBuilder<int>(
          stream: _stopWatchTimer!.rawTime,
          initialData: 0,
          builder: (context, snap) {
            final value = snap.data;
            final displayTime = StopWatchTimer.getDisplayTime(value!);
            return Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(0),
                  child: Text(
                    displayTime,
                    style: const TextStyle(
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            );
          },
        ));
      }
    }
    return Column(
      children: children,
    );
  }

  Widget _buildMiddleActionButtonsSection(SipCallState sipCallState, ColorTheme colorTheme) {
    if (sipCallState.status == SipPluginStatus.incomingCall) {
      return const SizedBox.shrink();
    } else if (sipCallState.status == SipPluginStatus.connecting) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: spacingExtraLarge),
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
        ),
      );
    }
    return _showNumpad
        ? Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  final colorTheme = themeState.colorTheme;
                  final textTheme = themeState.themeData.textTheme;
                  return CommonTextField(
                    readOnly: true,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    textEditingController: _numberController,
                    scrollController: _scrollController,
                    textFieldHeight: heightLarge,
                    textAlign: TextAlign.center,
                    textStyle: textTheme.displayLarge!.copyWith(color: colorTheme.onPrimaryColor),
                  );
                },
              ),
              const SizedBox(height: spacingExtraLarge),
              Numpad(
                callBack: (value) {
                  setState(() {
                    _numberController.text += value;
                  });
                  _voipCubit.sendDTMF(widget.handleId, value);
                },
              ),
            ],
          )
        : Column(
            children: [
              Wrap(
                alignment: WrapAlignment.center,
                children: [
                  // show transfer dialpad button
                  RoundShapeInkWell(
                    onTap: () {
                      _showTransferDialpadDialog(context);
                    },
                    color: colorTheme.primaryColor,
                    contentWidget: const Icon(Icons.phone_forwarded),
                  ),
                  // mute mic
                  RoundShapeInkWell(
                    onTap: _isMutingMic
                        ? null
                        : () async {
                            setState(() {
                              _isMutingMic = true;
                            });
                            try {
                              await _voipCubit.muteMicAudio(widget.handleId, !sipCallState.localAudioDisabled);
                            } finally {
                              if (mounted) {
                                setState(() {
                                  _isMutingMic = false;
                                });
                              }
                            }
                          },
                    color: colorTheme.primaryColor,
                    contentWidget: _isMutingMic
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                          )
                        : (sipCallState.localAudioDisabled ? const Icon(Icons.mic_off) : const Icon(Icons.mic)),
                  ),
                  // speaker
                  RoundShapeInkWell(
                    color: colorTheme.primaryColor,
                    contentWidget: AudioDeviceSelector(
                      callHandleId: widget.handleId,
                      colorTheme: colorTheme,
                    ),
                  ),
                  // hold call
                  RoundShapeInkWell(
                    onTap: _isHoldingCall
                        ? null
                        : () async {
                            setState(() {
                              _isHoldingCall = true;
                            });
                            try {
                              await _voipCubit.holdCall(widget.handleId);
                              _showOnholdCallDialog();
                            } finally {
                              if (mounted) {
                                setState(() {
                                  _isHoldingCall = false;
                                });
                              }
                            }
                          },
                    color: colorTheme.primaryColor,
                    contentWidget: _isHoldingCall
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                          )
                        : const Icon(Icons.pause),
                  ),
                ],
              ),
              // show numpad for dtmf
              RoundShapeInkWell(
                onTap: () {
                  setState(() {
                    _showNumpad = true;
                  });
                },
                color: colorTheme.primaryColor,
                contentWidget: const Icon(Icons.dialpad),
              ),
            ],
          );
  }

  Widget _buildBottomActionButtonsSection(SipCallState sipCallState, ColorTheme colorTheme) {
    if (sipCallState.status == SipPluginStatus.incomingCall) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // accept call button
          RoundShapeInkWell(
            color: colorTheme.connectedColor,
            contentWidget: _isAcceptingCall
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                  )
                : Icon(
                    Icons.call,
                    color: colorTheme.onPrimaryColor,
                    size: context.deviceWidth(0.04),
                  ),
            onTap: _isAcceptingCall
                ? null
                : () async {
                    setState(() {
                      _isAcceptingCall = true;
                    });
                    try {
                      await _voipCubit.acceptCall(widget.handleId);
                    } finally {
                      if (mounted) {
                        setState(() {
                          _isAcceptingCall = false;
                        });
                      }
                    }
                  },
          ),
          SizedBox(
            width: context.deviceWidth(0.1),
          ),
          // decline call button
          RoundShapeInkWell(
            checkNetwork: false,
            color: colorTheme.errorColor,
            contentWidget: _isDecliningCall
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                  )
                : Icon(
                    Icons.close,
                    color: colorTheme.onPrimaryColor,
                    size: context.deviceWidth(0.04),
                  ),
            onTap: _isDecliningCall
                ? null
                : () async {
                    setState(() {
                      _isDecliningCall = true;
                    });
                    try {
                      await _voipCubit.declineCall(widget.handleId);
                    } finally {
                      if (mounted) {
                        setState(() {
                          _isDecliningCall = false;
                        });
                      }
                    }
                  },
          ),
        ],
      );
    } else {
      final isHangingUp = sipCallState.status == SipPluginStatus.hangingup;
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const Spacer(),
          // complete transfer button
          if (sipCallState.transferData != null)
            RoundShapeInkWell(
              onTap: _isCompletingTransfer || sipCallState.status == SipPluginStatus.transfer
                  ? null
                  : () async {
                      setState(() {
                        _isCompletingTransfer = true;
                      });
                      try {
                        await _voipCubit.completeAttendedTransfer(widget.handleId);
                      } finally {
                        if (mounted) {
                          setState(() {
                            _isCompletingTransfer = false;
                          });
                        }
                      }
                    },
              checkNetwork: false,
              color: colorTheme.connectedColor,
              contentWidget: _isCompletingTransfer || sipCallState.status == SipPluginStatus.transfer
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                    )
                  : const Icon(Icons.call_merge),
            ),
          // hang up button
          Padding(
            padding: const EdgeInsets.all(0.0),
            child: RoundShapeInkWell(
              onTap: isHangingUp
                  ? null
                  : () async {
                      await _voipCubit.hangupCall(widget.handleId);
                    },
              checkNetwork: false,
              color: colorTheme.errorColor,
              contentWidget: isHangingUp
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                    )
                  : const Icon(Icons.call_end),
            ),
          ),
          Expanded(
            child: _showNumpad
                ? Align(
                    alignment: Alignment.centerLeft,
                    child: TextButton(
                        onPressed: () {
                          setState(() {
                            _showNumpad = false;
                          });
                        },
                        child: const Text('Hide Numpad')),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      );
    }
  }

  Widget _buildTranferSection(SipCallState sipCallState, ColorTheme colorTheme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        const Text(
          'Transfer Initiated',
          style: TextStyle(color: Colors.orange),
        ),
        SizedBox(height: context.deviceWidth(0.005)),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(sipCallState.transferData!.transfereeName),
            SizedBox(width: context.deviceWidth(0.01)),
            const Icon(Icons.east),
            SizedBox(width: context.deviceWidth(0.01)),
            Text(sipCallState.transferData!.transferTargetName),
          ],
        ),
        if (sipCallState.counterPartUri == sipCallState.transferData!.transfereeUri) ...[
          SizedBox(height: context.deviceWidth(0.005)),
          TextButton(
            onPressed: () async {
              await _voipCubit.cancelAttendedTransfer(widget.handleId);
            },
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.orange),
            ),
          ),
        ]
      ],
    );
  }

  void _showTransferDialpadDialog(BuildContext context) {
    if ((_voipCubit.state as VoipRunning)
            .activeCalls
            .firstWhereOrNull((s) => s.handleId == widget.handleId)
            ?.transferData !=
        null) {
      EasyLoadingService().showErrorWithText('Transfer already in progress.');
      return;
    }
    widget.controller.isTransferDialogVisible = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(32.0),
            ),
          ),
          insetPadding: const EdgeInsets.fromLTRB(48, 24, 24, 40),
          backgroundColor: const Color.fromARGB(255, 54, 54, 54),
          content: SizedBox(
            width: context.deviceWidth(isDesktop ? 0.85 : 1.0),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(child: TransferDialpad(handleId: widget.handleId)),
                  ],
                ),
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    onPressed: () {
                      pop(); // close trasnfer dialog
                    },
                    icon: const Icon(
                      Icons.close,
                      color: Color(0xFFD1D0CF),
                    ),
                    iconSize: 24,
                  ),
                )
              ],
            ),
          ),
        );
      },
    ).then((_) {
      widget.controller.isTransferDialogVisible = false;
    });
  }

  void _showOnholdCallDialog() {
    widget.controller.isHoldDialogOpen = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, themeState) {
            final colorTheme = themeState.colorTheme;

            return AlertDialog(
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(32.0),
                ),
              ),
              insetPadding: const EdgeInsets.fromLTRB(48, 24, 24, 40),
              backgroundColor: colorTheme.roundShapeInkWellColor,
              content: SizedBox(
                width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        const Spacer(),
                        const Text(
                          'Call on hold',
                          style: TextStyle(color: Colors.orange),
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        const Text(
                          'Press the play button to unhold',
                          style: TextStyle(color: Colors.orange),
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        Wrap(
                          alignment: WrapAlignment.center,
                          children: [
                            // show transfer dialog button
                            RoundShapeInkWell(
                              onTap: () {
                                _showTransferDialpadDialog(context);
                              },
                              color: colorTheme.primaryColor,
                              contentWidget: const Icon(Icons.phone_forwarded),
                            ),
                            // unhold call button
                            RoundShapeInkWell(
                              onTap: _isUnholdingCall
                                  ? null
                                  : () async {
                                      setState(() {
                                        _isUnholdingCall = true;
                                      });
                                      try {
                                        await _voipCubit.unholdCall(widget.handleId);
                                        pop();
                                      } finally {
                                        if (mounted) {
                                          setState(() {
                                            _isUnholdingCall = false;
                                          });
                                        }
                                      }
                                    },
                              color: colorTheme.primaryColor,
                              contentWidget: _isUnholdingCall
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
                                    )
                                  : const Icon(Icons.play_arrow),
                            ),
                          ],
                        ),
                        const Spacer(),
                        BlocSelector<VoipCubit, VoipState, SipCallState?>(selector: (state) {
                          if (state is VoipRunning) {
                            return state.activeCalls.firstWhereOrNull((s) => s.handleId == widget.handleId);
                          }
                          return null;
                        }, builder: (context, sipCallState) {
                          if (!mounted || sipCallState == null || sipCallState.transferData == null) {
                            return const SizedBox.shrink();
                          }
                          return _buildTranferSection(sipCallState, colorTheme);
                        }),
                      ],
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                        onPressed: () {
                          pop(); // close hold dialog
                          pop(); // close voip call dialog
                        },
                        icon: const Icon(
                          Icons.close,
                          color: Color(0xFFD1D0CF),
                        ),
                        iconSize: 24,
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        );
      },
    ).then((_) {
      widget.controller.isHoldDialogOpen = false;
    });
  }
}
