# Wake Up Flow

## iOS

It is the same for all cases: App foreground, background, background + lock screen, terminated, terminated + lock screen.

1. Receive APN VoIP push
2. FlutterCallkitIncoming show callkit (in native swift, AppDelegate.swift)
3. Home initState start to run
4. Perform SIP register
5. Receive SIP incoming call event
6. Accept/decline call

## Android

### App background/terminated

1. Receive FCM High Priority Push from background service
2. Background service start foreground service
3. Background service use FlutterCallkitIncoming to show callkit
4. Foreground service perform SIP register
5. Foreground service receive SIP incoming call event
6. Foreground service accept/decline call
7. Foreground service stop after call end

### App foreground

1. Receive FCM High Priority Push in main app
2. Main app perform SIP unregister
3. Main app start foreground service
4. Main app use FlutterCallkitIncoming to show callkit
5. Foreground service perform SIP register
6. Foreground service receive SIP incoming call event
7. Foreground service accept/decline call
8. Foreground service stop after call end
9. Main app perform SIP register

## Desktop

Always connected.

1. Main app perform SIP register on app start and maintain its connection throughout the app's lifetime
2. Main app receive SIP incoming call event
3. Main app accept/decline call

# Wake up timing

Below are the waiting times that involved in the wake up flow:

1. When caller make a call and FreeSwitch received it, ESL code will trigger to send APNS/FCM push

   🠋  Depends on Apple/Firebase server

2. Callee phone wake up by APNS/FCM push

   🠋  Depends on phone performance

3. Callee phone perform SIP register

   🠋  Depends on network condition and server performance

4. FreeSwitch register callee phone successfully

All steps must be completed within **X seconds** (defined in ESL code) for the call to behave normally.
