import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:flutter/material.dart';

/// A wrapper around RoundShapeInkWell that handles loading states automatically.
/// Shows a CircularProgressIndicator when an async operation is in progress.
class LoadingRoundShapeInkWell extends StatefulWidget {
  /// The async function to execute when tapped
  final Future<void> Function()? onTapAsync;
  
  /// The widget to show when not loading
  final Widget contentWidget;
  
  /// The widget to show when loading (defaults to CircularProgressIndicator)
  final Widget? loadingWidget;
  
  /// Color of the button
  final Color? color;
  
  /// Size of the button
  final double? size;
  
  /// Whether to check network connectivity
  final bool checkNetwork;
  
  /// Long press callback (not affected by loading state)
  final GestureLongPressCallback? onLongPress;

  const LoadingRoundShapeInkWell({
    super.key,
    required this.contentWidget,
    this.onTapAsync,
    this.loadingWidget,
    this.color,
    this.size,
    this.checkNetwork = true,
    this.onLongPress,
  });

  @override
  State<LoadingRoundShapeInkWell> createState() => _LoadingRoundShapeInkWellState();
}

class _LoadingRoundShapeInkWellState extends State<LoadingRoundShapeInkWell> {
  bool _isLoading = false;

  Future<void> _handleTap() async {
    if (_isLoading || widget.onTapAsync == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onTapAsync!();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return RoundShapeInkWell(
      onTap: _isLoading ? null : _handleTap,
      onLongPress: widget.onLongPress,
      color: widget.color,
      size: widget.size,
      checkNetwork: widget.checkNetwork,
      contentWidget: _isLoading
          ? (widget.loadingWidget ??
              const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  color: Colors.white,
                ),
              ))
          : widget.contentWidget,
    );
  }
}
