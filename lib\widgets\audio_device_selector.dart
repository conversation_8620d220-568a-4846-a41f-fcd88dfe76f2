import 'package:ddone/cubit/audio_device/audio_device_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/models/audio_device_model.dart';
import 'package:ddone/models/enums/audio_device_type_enum.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AudioDeviceSelector extends StatelessWidget {
  final int callHandleId;
  final bool showLabel;
  final ColorTheme? colorTheme;

  const AudioDeviceSelector({
    super.key,
    required this.callHandleId,
    this.colorTheme,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AudioDeviceCubit, AudioDeviceState>(
      builder: (context, state) {
        if (state is AudioDeviceLoading) {
          return CircularProgressIndicator(
            color: colorTheme?.onSurfaceColor ?? Colors.white,
            strokeWidth: 2,
          );
        }

        if (state is AudioDeviceError) {
          return Icon(Icons.error, color: colorTheme?.errorColor ?? Colors.red);
        }

        final availableDevices = state.availableDevices;

        if (availableDevices.isEmpty) {
          return Icon(
            Icons.volume_off,
            color: colorTheme?.onSurfaceColor ?? Colors.white,
          );
        }

        // For mobile, show simple toggle between earpiece and speaker
        if (isMobile && availableDevices.length <= 2) {
          return _buildSpeakerEarpieceToggle(context, state);
        }

        // For mobile when multiple devices available, show dropdown
        return _buildDeviceDropdown(context, state);
      },
    );
  }

  Widget _buildSpeakerEarpieceToggle(BuildContext context, AudioDeviceState state) {
    final audioDeviceCubit = context.read<AudioDeviceCubit>();
    final isLoudSpeaker = audioDeviceCubit.isPhoneSpeakerOn;

    return GestureDetector(
      onTap: () {
        audioDeviceCubit.toggleSpeakerPhone(callHandleId);
      },
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isLoudSpeaker ? Icons.volume_up : Icons.volume_off,
                color: colorTheme?.onSurfaceColor ?? Colors.white,
              ),
              if (showLabel) ...[
                const SizedBox(height: 4),
                Text(
                  isLoudSpeaker ? 'Speaker' : 'Earpiece',
                  style: TextStyle(
                    color: colorTheme?.onSurfaceColor ?? Colors.white,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceDropdown(BuildContext context, AudioDeviceState state) {
    final audioDeviceCubit = context.read<AudioDeviceCubit>();
    final selectedDevice = state.selectedDevice;
    final availableDevices = state.availableDevices;

    return PopupMenuButton<AudioDeviceModel>(
        onSelected: (device) {
          audioDeviceCubit.selectDevice(callHandleId, device);
        },
        itemBuilder: (context) {
          return availableDevices.map((device) {
            final isCurrentlySelected = selectedDevice?.deviceId == device.deviceId;
            return PopupMenuItem<AudioDeviceModel>(
              value: device,
              child: Row(
                children: [
                  Icon(
                    _getDeviceIcon(device),
                    size: 20,
                    color: isCurrentlySelected ? colorTheme?.primaryColor ?? const Color(0xFFffb000) : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      device.label,
                      style: TextStyle(
                        fontWeight: isCurrentlySelected ? FontWeight.bold : FontWeight.normal,
                        color: isCurrentlySelected ? colorTheme?.primaryColor ?? const Color(0xFFffb000) : null,
                      ),
                    ),
                  ),
                  if (isCurrentlySelected)
                    Icon(
                      Icons.check,
                      size: 16,
                      color: colorTheme?.primaryColor ?? const Color(0xFFffb000),
                    ),
                ],
              ),
            );
          }).toList();
        },
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  selectedDevice != null ? Icons.volume_up : Icons.volume_off,
                  color: colorTheme?.onSurfaceColor ?? Colors.white,
                  // size: iconSize,
                ),
                if (showLabel) ...[
                  const SizedBox(height: 4),
                  Text(
                    selectedDevice?.label ?? 'No Device',
                    style: TextStyle(
                      color: colorTheme?.onSurfaceColor ?? Colors.white,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ));
  }

  IconData _getDeviceIcon(AudioDeviceModel device) {
    switch (device.type) {
      case AudioDeviceType.wiredHeadphones:
        return Icons.headphones;
      case AudioDeviceType.bluetoothHeadphones:
        return Icons.bluetooth_audio;
      case AudioDeviceType.bluetoothSpeaker:
        return Icons.speaker;
      case AudioDeviceType.earpiece:
        return Icons.phone;
      case AudioDeviceType.speaker:
        return Icons.volume_up;
      case AudioDeviceType.systemDefault:
        return Icons.computer;
      case AudioDeviceType.none:
        return Icons.volume_off;
      case AudioDeviceType.other:
        return Icons.speaker;
    }
  }
}
