enum VoipSipEvent {
  registered,
  unregistered,
  transferCall,
  hangingup,
  hangup,
  ringing,
  proceeding,
  calling,
  progress,
  accepted,
  loading,
  incomingCall,
}

extension VoipSipEventExtension on VoipSipEvent {
  String statusMessage() {
    switch (this) {
      case VoipSipEvent.accepted:
        return 'Call Connected!';
      case VoipSipEvent.progress:
        return 'Call Connected!';
      case VoipSipEvent.calling:
        return 'Calling...';
      case VoipSipEvent.proceeding:
        return 'Proceeding...';
      case VoipSipEvent.hangingup:
        return 'Hanging up...';
      case VoipSipEvent.hangup:
        return 'Call Ended!';
      case VoipSipEvent.loading:
        return 'Connecting...';
      case VoipSipEvent.ringing:
        return 'Ringing...';
      case VoipSipEvent.incomingCall:
        return 'Incoming Call!';
      default:
        return '';
    }
  }
}
