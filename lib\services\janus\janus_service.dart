import 'dart:convert';

import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/services/janus/sip_plugin_manager.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/priority_stream_manager.dart';
import 'package:flutter/services.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:janus_client/janus_client.dart';

class JanusService {
  // static final singleton pattern
  static final JanusService _instance = JanusService._internal();
  JanusService._internal();
  factory JanusService() {
    return _instance;
  }

  final PriorityStreamManager<List<SipCallState>> _priorityStreamManager = PriorityStreamManager<List<SipCallState>>();

  bool _isInitializing = false;
  bool _isDisposing = false;

  WebSocketJanusTransport? _webSocketJanusTransport;
  JanusClient? _janusClient;
  JanusSession? _janusSession;
  SipPluginManager? _sipPluginManager;
  dynamic _websocketMessage;
  DateTime? _lastWebsocketMessageReceivedTime;

  PriorityStreamManager<List<SipCallState>> get prioritySipCallsStateStream => _priorityStreamManager;
  bool get isReady => _sipPluginManager?.isMasterReady() ?? false;

  // For future vidoe call use
  RTCVideoRenderer remoteVideoRenderer = RTCVideoRenderer();

  Future<bool> init({
    required String sipWsUrl,
    required String sipNumber,
    required String sipDomain,
    required String sipProxy,
    required String sipSecret,
    required String sipName,
    required String sipHeaderToken,
  }) async {
    if (_isInitializing) return false; // prevent double initialization simultaneously
    if (await checkJanusConnection()) {
      log.t('Janus service init skipped, already has janus connection.');
      return false;
    } // don't initialize when already has a session
    try {
      _isInitializing = true;

      _webSocketJanusTransport = WebSocketJanusTransport(url: sipWsUrl);
      _janusClient = JanusClient(
        transport: _webSocketJanusTransport!,
        isUnifiedPlan: true,
      );
      _janusSession = await _janusClient!.createSession();

      // listen to websoket connection
      _webSocketJanusTransport?.stream.listen((message) {
        var messageJson = jsonDecode(message);
        _websocketMessage = messageJson;
        _lastWebsocketMessageReceivedTime = DateTime.now();
      });

      _sipPluginManager = SipPluginManager(_janusSession!);
      await _sipPluginManager!.init(
        username: 'sip:$sipNumber@$sipDomain',
        proxy: 'sip:$sipProxy',
        secret: sipSecret,
        sipName: sipName,
        sipHeaderToken: sipHeaderToken,
      );

      _priorityStreamManager.startListening(_sipPluginManager!.sipCallsStateStream);
    } catch (e) {
      log.e('janus init error: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
    return true;
  }

  Future<bool> checkJanusConnection() async {
    try {
      if (_webSocketJanusTransport == null || _janusClient == null || _janusSession == null) {
        return false;
      }
      // this check whether websocket is connected.
      bool websocketIsConnected = _webSocketJanusTransport?.isConnected ?? false;
      if (!websocketIsConnected) {
        log.t('websocket is not connected');
        return false;
      }
      // check when does we last receive websocket message from janus server.
      // too long no message means that we already disconnected from janus server.
      // - JanusClient send keep alive message every 50 seconds.
      if (_lastWebsocketMessageReceivedTime != null &&
          DateTime.now().difference(_lastWebsocketMessageReceivedTime!).inSeconds > 60) {
        log.t('Too long no message from janus server.');
        return false;
      }
      // this check whether the session has error.
      String websocketMessageStatus = _websocketMessage?['janus'];
      bool janusSessionError = websocketMessageStatus == 'error';
      if (janusSessionError) {
        log.t('janus session recoded error. _websocketMessage:$_websocketMessage');
        return false;
      }
      // TODO: need to check registration status in fusion too.
      return true;
    } catch (e) {
      log.w('checkJanusConnection error', error: e);
      return false;
    }
  }

  Future<void> dispose() async {
    await waitForCondition(() async => !_isDisposing, timeout: const Duration(seconds: 3));
    if (_janusSession == null && _webSocketJanusTransport == null) {
      log.t('JanusService already disposed, skipping');
      return;
    }

    try {
      log.t('disposing janus in janusService');
      _isDisposing = true;
      try {
        await _sipPluginManager?.unregister();
        await waitForCondition(() async => _sipPluginManager?.isMasterUnregistered() ?? false,
            timeout: const Duration(seconds: 5));
        _priorityStreamManager.dispose();
        await _sipPluginManager?.disposeAll();
      } on PlatformException catch (e) {
        if (e.code == 'MediaStreamDisposeFailed') {
          // looks like is janus_plugin bug, specific to windows app.
          // doesn't affect anything, so just catch it, log it as warn, and let it continue to run.
          log.w('MediaStreamDisposeFailed', error: e);
        } else {
          rethrow;
        }
      } on NoSuchMethodError catch (e) {
        if (e.toString().contains('[]("janus")')) {
          // When we already disconnected for janus server and we want to again unregister from it
          // janus server will return null as response. This cause the JanusEvent.fromJson() in unregister method
          // throw: "The method '[]' was called on null. ...... Tried calling: []("janus")' error."
          // So can just ignore this because it is means that we already unregistered.
          log.w('NoSuchMethodError', error: e);
        } else {
          rethrow;
        }
      } catch (e) {
        final errorMessage = e.toString();
        if (errorMessage.contains('getTransceivers() peerConnection is null') ||
            errorMessage.contains('MediaStreamTrack has been disposed')) {
          // In windows app when user decline call, _janusSipPlugin.peerConnection will be null.
          // Seems like is janus_plugin bug in windows platform, so just catch it and log it as warn.
          // As long as later _janusSipPlugin get reinitialized, then it will be fine.
          // Also handle MediaStreamTrack disposal errors during service shutdown
          log.w('WebRTC/MediaStream error during disposal', error: errorMessage);
        } else {
          rethrow;
        }
      }
      _webSocketJanusTransport?.dispose();
      _janusSession?.dispose();

      _webSocketJanusTransport = null;
      _janusClient = null;
      _janusSession = null;
    } catch (e) {
      log.e('Failed to dispose JanusService', error: e);
      rethrow;
    } finally {
      _isDisposing = false;
      log.t('disposing janus in janusService complete');
    }
  }

  Future<int> makeCall(String displayName, String extNum, String sipProxy) async {
    int handleId = await _sipPluginManager!.makeCall(displayName, extNum, sipProxy);
    return handleId;
  }

  Future<void> hangupCall(int handleId) async {
    await _sipPluginManager!.hangupCall(handleId);
  }

  Future<void> acceptCall(int handleId) async {
    await _sipPluginManager!.acceptCall(handleId);
  }

  Future<void> declineCall(int handleId) async {
    await _sipPluginManager!.declineCall(handleId);
  }

  Future<void> blindTransferCall(int handleId, String extNum, String sipProxy) async {
    await _sipPluginManager!.blindTransferCall(handleId, extNum, sipProxy);
  }

  Future<void> initiateAttendedTransfer(int handleId, String displayName, String extNum, sipProxy) async {
    await _sipPluginManager!.initiateAttendedTransfer(handleId, displayName, extNum, sipProxy);
  }

  Future<void> completeAttendedTransfer(int handleId) async {
    await _sipPluginManager!.completeAttendedTransfer(handleId);
  }

  Future<void> cancelAttendedTransfer(int handleId) async {
    await _sipPluginManager!.cancelAttendedTransfer(handleId);
  }

  Future<void> holdCall(int handleId) async {
    await _sipPluginManager!.holdCall(handleId);
  }

  Future<void> unholdCall(int handleId) async {
    await _sipPluginManager!.unholdCall(handleId);
  }

  Future<void> toggleHoldCall(int handleId) async {
    await _sipPluginManager!.toggleHoldCall(handleId);
  }

  Future<void> sendDTMF(int handleId, String tone) async {
    await _sipPluginManager!.sendDTMF(handleId, tone);
  }

  Future<void> muteMicAudio(int handleId, bool mute) async {
    await _sipPluginManager!.muteMicAudio(handleId, mute);
  }

  Future<void> muteSpeakerAudio(int handleId, bool mute) async {
    await _sipPluginManager!.muteSpeakerAudio(handleId, mute);
  }
}
