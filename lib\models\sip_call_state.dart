import 'package:ddone/models/sip_transfer_model.dart';
import 'package:equatable/equatable.dart';

enum SipPluginStatus {
  initial(0, 'Initial'),
  registered(1, 'Registered'),
  callInitiated(2, 'Calling...'),
  calling(2, 'Calling...'),
  proceeding(2, 'Proceeding...'),
  connecting(2, 'Connecting...'),
  ringing(2, 'Ringing...'),
  incomingCall(2, 'Incoming call'),
  progress(2, 'Progressing...'),
  accepted(3, 'Call Connected'),
  transfer(4, 'Transfering...'),
  hangingup(5, 'Hanging Up...'),
  hungup(6, 'Hang Up'),
  missedCall(7, 'Missed Call'),
  unregistered(8, 'Unregistered'),
  disposing(9, 'Dispsosing...'),
  disposed(10, 'Dispossed'),
  error(11, 'Error');

  final int value;
  final String display;
  const SipPluginStatus(this.value, this.display);

  bool get isCallActive {
    return value >= SipPluginStatus.callInitiated.value && value < SipPluginStatus.hungup.value;
  }

  static SipPluginStatus fromValue(int value) {
    return SipPluginStatus.values.firstWhere(
      (s) => s.value == value,
      orElse: () => SipPluginStatus.error,
    );
  }
}

class SipCallState extends Equatable {
  final int handleId;
  final bool isHelper;
  final SipPluginStatus status;
  final String callId;
  final String counterPartUri;
  final String counterPartName;
  final int startTimestamp; // microseconds timestamp
  final bool onhold;
  final bool localAudioDisabled;
  final bool remoteAudioDisabled;
  final SipTransferModel? transferData;

  const SipCallState({
    required this.handleId,
    required this.isHelper,
    required this.status,
    required this.startTimestamp,
    this.callId = '',
    this.counterPartUri = '',
    this.counterPartName = '',
    this.onhold = false,
    this.localAudioDisabled = false,
    this.remoteAudioDisabled = false,
    this.transferData,
  });

  @override
  List<Object?> get props => [
        handleId,
        isHelper,
        status,
        callId,
        counterPartUri,
        counterPartName,
        startTimestamp,
        onhold,
        localAudioDisabled,
        remoteAudioDisabled,
        transferData,
      ];

  SipCallState copyWith({
    bool? isHelper,
    SipPluginStatus? status,
    String? callId,
    String? counterPartUri,
    String? counterPartName,
    int? startTimestamp,
    bool? onhold,
    bool? localAudioDisabled,
    bool? remoteAudioDisabled,
    SipTransferModel? transferData,
  }) {
    return SipCallState(
      handleId: handleId,
      isHelper: isHelper ?? this.isHelper,
      status: status ?? this.status,
      callId: callId ?? this.callId,
      counterPartUri: counterPartUri ?? this.counterPartUri,
      counterPartName: counterPartName ?? this.counterPartName,
      startTimestamp: startTimestamp ?? this.startTimestamp,
      onhold: onhold ?? this.onhold,
      localAudioDisabled: localAudioDisabled ?? this.localAudioDisabled,
      remoteAudioDisabled: remoteAudioDisabled ?? this.remoteAudioDisabled,
      transferData: transferData ?? this.transferData,
    );
  }

  Map<String, dynamic> toJson() => {
        'handleId': handleId,
        'isHelper': isHelper,
        'status': status.value,
        'callId': callId,
        'counterPartUri': counterPartUri,
        'counterPartName': counterPartName,
        'startTimestamp': startTimestamp,
        'onhold': onhold,
        'localAudioDisabled': localAudioDisabled,
        'remoteAudioDisabled': remoteAudioDisabled,
        'transferData': transferData?.toJson(),
      };

  factory SipCallState.fromJson(Map<String, dynamic> json) => SipCallState(
        handleId: json['handleId'] as int,
        isHelper: json['isHelper'] as bool,
        status: SipPluginStatus.fromValue(json['status'] as int),
        callId: json['callId'] as String? ?? '',
        counterPartUri: json['counterPartUri'] as String? ?? '',
        counterPartName: json['counterPartName'] as String? ?? '',
        startTimestamp: json['startTimestamp'] as int? ?? 0,
        onhold: json['onhold'] as bool? ?? false,
        localAudioDisabled: json['localAudioDisabled'] as bool? ?? false,
        remoteAudioDisabled: json['remoteAudioDisabled'] as bool? ?? false,
        transferData: json['transferData'] != null
            ? SipTransferModel.fromJson(json['transferData'] as Map<String, dynamic>)
            : null,
      );
}
