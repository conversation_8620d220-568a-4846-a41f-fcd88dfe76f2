import 'package:collection/collection.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/dialpad_bottom_buttons.dart';
import 'package:ddone/components/list_tile/card_list_tile_with_title.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/focus/focus_cubit.dart';
import 'package:ddone/cubit/transfer_dialpad/transfer_dialpad_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Constants for magic numbers
class _TransferDialpadConstants {
  static const double contactsWidthRatio = 0.4;
  static const double dividerWidthRatio = 0.003;
  static const double dividerHeightRatio = 0.88;
  static const double verticalSpacingRatio = 0.03;
}

enum TransferType { attended, blind }

class TransferDialpad extends StatefulWidget {
  final int handleId;

  const TransferDialpad({super.key, required this.handleId});

  @override
  State<TransferDialpad> createState() => _TransferDialpadState();
}

class _TransferDialpadState extends State<TransferDialpad>
    with AutomaticKeepAliveClientMixin<TransferDialpad>, PrefsAware {
  @override
  bool get wantKeepAlive => true;

  late final TextEditingController _textController;
  late final TextEditingController _searchNameController;
  late final TransferDialpadCubit _transferDialpadCubit;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeCubits();
  }

  void _initializeControllers() {
    _textController = TextEditingController();
    _searchNameController = TextEditingController();
  }

  void _initializeCubits() {
    final contactsCubit = context.read<ContactsCubit>();
    _transferDialpadCubit = TransferDialpadCubit.initial();
    _transferDialpadCubit.getContacts(contactsCubit);
  }

  @override
  void dispose() {
    _textController.dispose();
    _searchNameController.dispose();
    _transferDialpadCubit.close();
    super.dispose();
  }

  void _handleNumberInput(String number) {
    setState(() {
      _textController.text += number;
    });
  }

  void _handleBackspace({bool deleteAll = false}) {
    setState(() {
      final text = _textController.text;
      if (text.isNotEmpty) {
        _textController.text = deleteAll ? '' : text.substring(0, text.length - 1);
      }
    });
  }

  void _handleBlindTransferAction() {
    if (_textController.text.isNotEmpty) {
      final sipProxy = prefs.getString(CacheKeys.sipProxy);
      if (sipProxy == null) return;

      context.read<VoipCubit>().blindTransferCall(
            widget.handleId,
            _textController.text,
            sipProxy,
          );

      _textController.clear();
      if (mounted) Navigator.of(context).pop();
    }
  }

  void _handleAttendedTransferAction() {
    if (_textController.text.isNotEmpty) {
      final sipProxy = prefs.getString(CacheKeys.sipProxy);
      if (sipProxy == null) return;

      final displayName = _transferDialpadCubit.state.filteredContactModelList
              .firstWhereOrNull(
                (c) => c.contactId == _textController.text,
              )
              ?.displayName ??
          _textController.text;
      context.read<VoipCubit>().initiateAttendedTransfer(widget.handleId, displayName, _textController.text, sipProxy);

      _textController.clear();
      if (mounted) Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (!isMobile) _buildContactsList(),
        if (!isMobile) _buildDivider(),
        Expanded(child: _buildDialpad()),
      ],
    );
  }

  Widget _buildContactsList() {
    return SizedBox(
      width: context.deviceWidth(_TransferDialpadConstants.contactsWidthRatio),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildSearchField(),
          Expanded(child: _buildFilteredContactsList()),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(8, 24, 8, 8),
        child: CommonTextField(
          textEditingController: _searchNameController,
          onValueChanged: (value) => _transferDialpadCubit.filterContactList(value),
          onFocusChange: (hasFocus) {
            final focusCubit = context.read<FocusCubit>();
            hasFocus ? focusCubit.focusTransferContactField() : focusCubit.unfocusTransferContactField();
          },
          hintText: 'Search',
          prefixIcon: const Icon(Icons.search, size: iconSizeMedium),
        ),
      ),
    );
  }

  Widget _buildFilteredContactsList() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 8, 24, 24),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return ScrollbarTheme(
            data: ScrollbarThemeData(
              thumbColor: WidgetStatePropertyAll(Colors.grey.shade700),
            ),
            child: BlocBuilder<TransferDialpadCubit, TransferDialpadState>(
              bloc: _transferDialpadCubit,
              builder: (context, state) {
                return ListView.builder(
                  itemCount: state.filteredContactModelList.length,
                  itemBuilder: (context, index) => _buildContactItem(
                    context,
                    state.filteredContactModelList,
                    index,
                    themeState.colorTheme,
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context,
    List<dynamic> contacts,
    int index,
    ColorTheme colorTheme,
  ) {
    final contact = contacts[index];
    final displayName = contact.displayName;
    final showTitle = _shouldShowTitle(contacts, index);

    return CardListTileWithTitle(
      title: displayName[0].toUpperCase(),
      listTileTitle: displayName,
      leadingIcon: Icon(Icons.person_outline, color: colorTheme.primaryColor),
      onTap: () {
        setState(() {
          _textController.text = contact.contactId;
        });
      },
      showTitle: showTitle,
    );
  }

  bool _shouldShowTitle(List<dynamic> contacts, int index) {
    if (index == 0) return true;

    final currentContact = contacts[index];
    final previousContact = contacts[index - 1];

    return !currentContact.displayName.toLowerCase().startsWith(previousContact.displayName[0].toLowerCase());
  }

  Widget _buildDivider() {
    return SizedBox(
      width: context.deviceWidth(_TransferDialpadConstants.dividerWidthRatio),
      height: context.deviceHeight(_TransferDialpadConstants.dividerHeightRatio),
      child: Container(color: Colors.black),
    );
  }

  Widget _buildDialpad() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildNumberInput(),
        SizedBox(height: context.deviceHeight(_TransferDialpadConstants.verticalSpacingRatio)),
        Numpad(callBack: _handleNumberInput),
        _buildBottomButtons(),
        SizedBox(height: context.deviceHeight(_TransferDialpadConstants.verticalSpacingRatio)),
      ],
    );
  }

  Widget _buildNumberInput() {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        return CommonTextField(
          onFocusChange: (hasFocus) {
            final focusCubit = context.read<FocusCubit>();
            hasFocus ? focusCubit.focusTransferDialpadField() : focusCubit.unfocusTransferDialpadField();
          },
          textEditingController: _textController,
          textFieldHeight: heightLarge,
          textAlign: TextAlign.center,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          textStyle: themeState.themeData.textTheme.displayLarge!.copyWith(color: themeState.colorTheme.onPrimaryColor),
        );
      },
    );
  }

  Widget _buildBottomButtons() {
    return DialpadBottomButtons(
      onLeftButtonClick: _handleAttendedTransferAction,
      onMiddleButtonClick: _handleBlindTransferAction,
      onRightButtonClick: () => _handleBackspace(),
      onRightButtonLongPress: () => _handleBackspace(deleteAll: true),
      leftIcon: Icons.add_ic_call,
      middleIcon: Icons.phone_forwarded,
      rightIcon: Icons.backspace_outlined,
      leftButtonColor: Colors.green,
    );
  }
}
