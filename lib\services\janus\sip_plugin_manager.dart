import 'dart:async';

import 'package:collection/collection.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/models/sip_transfer_model.dart';
import 'package:ddone/services/janus/janus_service_exception.dart';
import 'package:ddone/services/janus/sip_plugin_wrapper.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:janus_client/janus_client.dart';
import 'package:rxdart/rxdart.dart';

class SipPluginManager {
  final JanusSession _janusSession;
  final Map<int, SipPluginWrapper> _sipPluginWrappers = {};
  final Map<int, StreamSubscription> _sipCallStateSubscriptions = {};
  final _sipCallsStateController = BehaviorSubject<List<SipCallState>>();
  String? _username;
  String? _proxy;
  String? _secret;
  String? _sipName;
  String? _sipHeaderToken;
  MediaStream? localStream;

  bool _maintainOneReadySessionRunning = false;
  final Set<int> _isDisposing = {};
  bool _isDisposingLocalMedia = false;
  bool _isReinitRunning = false;

  /// Stream out all SipPluginWrapper's SipCallState
  Stream<List<SipCallState>> get sipCallsStateStream => _sipCallsStateController.stream;

  SipPluginManager(this._janusSession);

  Future<void> init({
    required String username,
    required String proxy,
    required String secret,
    String? sipName,
    String? sipHeaderToken,
    bool ignoreReinit = false,
  }) async {
    if (_isReinitRunning && !ignoreReinit) {
      log.t('reinit is running. Skipped init');
      return;
    }
    await _createSipPlugin(
      username: username,
      proxy: proxy,
      secret: secret,
      sipName: sipName,
      sipHeaderToken: sipHeaderToken,
    );
    // save it for create helper session + reinit purpose later
    _username = username;
    _proxy = proxy;
    _secret = secret;
    _sipName = sipName;
    _sipHeaderToken = sipHeaderToken;
  }

  /// Create a SIP plugin and register it with Janus server.
  /// The first created SIP plugin will be the master session, all subsequent created SIP plugin will be helper session.
  Future<SipPluginWrapper> _createSipPlugin({
    required String username,
    required String proxy,
    required String secret,
    String? sipName,
    String? sipHeaderToken,
  }) async {
    final janusSipPlugin = await _janusSession.attach<JanusSipPlugin>();

    // wait a bit for _janusSipPlugin to be ready.
    bool sipPluginIsReady =
        await waitForCondition(() async => janusSipPlugin.remoteTrack != null && janusSipPlugin.typedMessages != null);
    if (!sipPluginIsReady) {
      log.w('remoteTrack and typedMessages may not be ready. JanusSipPlugin may have issue.');
    }

    SipPluginWrapper sipPluginWrapper = SipPluginWrapper(janusSipPlugin);
    log.t('Created SIP Plugin with handle id: ${sipPluginWrapper.handleId}');
    if (sipPluginWrapper.handleId == null) {
      throw const JanusSipPluginException(
          'Invalid sipPluginWrapper.handleId after attaching JanusSipPlugin to JanusSession');
    }

    if (hasMaster()) {
      bool masterIsReady = await waitForCondition(() async => isMasterReady(), timeout: const Duration(seconds: 10));
      if (masterIsReady) {
        await sipPluginWrapper.register(
          username: username,
          proxy: proxy,
          secret: secret,
          sipName: sipName,
          sipHeaderToken: sipHeaderToken,
          masterId: getMasterId(),
        );
      } else {
        throw const JanusSipPluginException(
            'Cannot create helper session. Master SIP session exist but not ready after 10 seconds');
      }
    } else {
      await sipPluginWrapper.register(
        username: username,
        proxy: proxy,
        secret: secret,
        sipName: sipName,
        sipHeaderToken: sipHeaderToken,
      );
    }

    _sipPluginWrappers[sipPluginWrapper.handleId!] = sipPluginWrapper;
    _sipCallStateSubscriptions[sipPluginWrapper.handleId!] =
        sipPluginWrapper.sipCallStateStream.listen((newSipCallState) {
      _boardcastSipCallsState();
      _releaseMediaWhenNoActiveCalls();
      _clearTransferDataWhenHungup();
      _disposeEndedHelpers();
      _manageHelperSession();
      _reinitWhenAllCallEnded();
    });
    return sipPluginWrapper;
  }

  void _boardcastSipCallsState() {
    _sipCallsStateController.add(_sipPluginWrappers.values.map((w) => w.currSipCallState).toList());
  }

  /// Initialize local media devices.
  /// - We are doing this manually instead of using the janusSipPlugin.initializeMediaDevices()
  ///   because we want to use the same localStream for all the SIP plugin to avoid having performance overhead.
  Future<MediaStream> _initLocalMedia() async {
    if (localStream != null) {
      return localStream!;
    }
    localStream = await navigator.mediaDevices.getUserMedia({'audio': true, 'video': false});
    return localStream!;
  }

  Future<void> _disposeLocalMedia() async {
    if (_isDisposingLocalMedia) return;
    if (localStream != null) {
      try {
        _isDisposingLocalMedia = true;
        await stopAllTracks(localStream);
        await localStream?.dispose();
        localStream = null;
      } catch (e) {
        log.e('Failed to dispose local media', error: e);
      } finally {
        _isDisposingLocalMedia = false;
      }
    }
  }

  bool hasMaster() {
    return _sipPluginWrappers.values.any((w) => w.isMaster);
  }

  bool isMasterReady() {
    return _sipPluginWrappers.values.firstWhereOrNull((w) => w.isMaster && w.masterId != null) != null;
  }

  int? getMasterId() {
    return _sipPluginWrappers.values.firstWhereOrNull((w) => w.isMaster && w.masterId != null)?.masterId;
  }

  Future<void> dispose(int handleId, {bool ignoreReinit = false}) async {
    if (_isReinitRunning && !ignoreReinit) {
      log.t('reinit is running. Skipped dispose');
      return;
    }
    if (_isDisposing.contains(handleId)) {
      log.t('SIP plugin $handleId already disposing. Skipped dispose');
      return;
    }
    try {
      _isDisposing.add(handleId);
      await _sipPluginWrappers[handleId]?.dispose();
      await _sipCallStateSubscriptions[handleId]?.cancel();
    } catch (e) {
      log.e('Failed to dispose SIP plugin $handleId', error: e);
    }
    _sipCallStateSubscriptions.remove(handleId);
    _sipPluginWrappers.remove(handleId);
    _isDisposing.remove(handleId);

    _boardcastSipCallsState();
  }

  Future<void> disposeAll() async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped disposeAll');
      return;
    }
    await Future.wait(_sipPluginWrappers.values.map((w) => dispose(w.handleId!)));
    await _disposeLocalMedia();
    _sipPluginWrappers.clear();
    _sipCallStateSubscriptions.clear();
    await _sipCallsStateController.close();
  }

  Future<void> unregister({bool ignoreReinit = false}) async {
    if (_isReinitRunning && !ignoreReinit) {
      log.t('reinit is running. Skipped unregister');
      return;
    }
    // helper session don't actually register to SIP server, so only need to unregister master session
    SipPluginWrapper? master = _sipPluginWrappers.values.firstWhereOrNull((w) => w.isMaster);
    await master?.unregister();
  }

  bool isMasterUnregistered() {
    SipPluginWrapper? master = _sipPluginWrappers.values.firstWhereOrNull((w) => w.isMaster);
    return master?.currSipCallState.status == SipPluginStatus.unregistered;
  }

  /// Maintain one helper sessions
  /// - When in call: have exactly 1 ready helper session
  /// - When not in call: should not have helper session
  Future<void> _manageHelperSession() async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped maintainOneReadySession');
      return;
    }
    if (!isMasterReady() || _maintainOneReadySessionRunning) return;
    try {
      _maintainOneReadySessionRunning = true;
      SipPluginWrapper? active =
          _sipPluginWrappers.values.firstWhereOrNull((w) => w.currSipCallState.status.isCallActive);
      if (active != null) {
        Iterable<SipPluginWrapper>? ready = _sipPluginWrappers.values
            .where((w) => w.isHelper && w.currSipCallState.status.value <= SipPluginStatus.registered.value);
        if (ready.isEmpty) {
          await _createSipPlugin(
            username: _username!,
            proxy: _proxy!,
            secret: _secret!,
            sipName: _sipName,
            sipHeaderToken: _sipHeaderToken,
          );
        } else if (ready.length > 1) {
          // keep one and remove the remaining of it
          await Future.wait(ready.skip(1).map((w) => dispose(w.handleId!)));
        }
      } else {
        // dipose all helpers
        await Future.wait(_sipPluginWrappers.values.where((w) => w.isHelper).map((w) => dispose(w.handleId!)));
      }
    } catch (e) {
      log.e('Failed to maintain one ready session', error: e);
    } finally {
      _maintainOneReadySessionRunning = false;
    }
  }

  Future<void> _clearTransferDataWhenHungup() async {
    Iterable<SipPluginWrapper>? hungups =
        _sipPluginWrappers.values.where((w) => w.currSipCallState.status == SipPluginStatus.hungup);
    await Future.wait(hungups.map((w) async {
      if (w.currSipCallState.transferData != null) {
        cancelAttendedTransfer(w.handleId!);
      }
    }));
  }

  Future<void> _disposeEndedHelpers() async {
    Iterable<SipPluginWrapper>? helpers = _sipPluginWrappers.values
        .where((w) => w.isHelper && w.currSipCallState.status.value >= SipPluginStatus.hungup.value);
    await Future.wait(helpers.map((w) => dispose(w.handleId!)));
  }

  Future<void> _releaseMediaWhenNoActiveCalls() async {
    Iterable<SipPluginWrapper>? activeSipPlugins =
        _sipPluginWrappers.values.where((w) => w.currSipCallState.status.isCallActive);
    if (activeSipPlugins.isEmpty) {
      _disposeLocalMedia();
    }
  }

  Future<void> _reinitWhenAllCallEnded() async {
    if (_isReinitRunning) return;
    SipPluginWrapper? hungupMaster = _sipPluginWrappers.values
        .firstWhereOrNull((w) => w.isMaster && w.currSipCallState.status == SipPluginStatus.hungup);
    Iterable<SipPluginWrapper>? actives =
        _sipPluginWrappers.values.where((w) => w.currSipCallState.status.isCallActive);
    if (hungupMaster != null && actives.isEmpty) {
      try {
        _isReinitRunning = true;
        log.t('reinitWhenAllCallEnded - unregistering master');
        // unregister master
        await unregister(ignoreReinit: true);
        await waitForCondition(() async => isMasterUnregistered(), timeout: const Duration(seconds: 5));
        log.t('reinitWhenAllCallEnded - disposing');
        // dispose master
        await dispose(hungupMaster.handleId!, ignoreReinit: true);
        // dispose helper (if any)
        await Future.wait(_sipPluginWrappers.values.where((w) => w.isHelper).map((w) => dispose(w.handleId!)));
        log.t('reinitWhenAllCallEnded - recreating master');
        // recreate master
        await _createSipPlugin(
          username: _username!,
          proxy: _proxy!,
          secret: _secret!,
          sipName: _sipName,
          sipHeaderToken: _sipHeaderToken,
        );
      } catch (e) {
        log.e('Failed to reinit master SIP plugin', error: e);
      } finally {
        _isReinitRunning = false;
        log.t('reinitWhenAllCallEnded - completed');
      }
    }
  }

  Future<int> makeCall(String displayName, String extNum, String sipProxy) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped makeCall');
      throw const JanusSipPluginException('Cannot make call. Reinit is running.');
    }
    SipPluginWrapper? sipPluginWrapper =
        _sipPluginWrappers.values.firstWhereOrNull((w) => w.currSipCallState.status == SipPluginStatus.registered);
    if (sipPluginWrapper == null) {
      throw const JanusSipPluginException('No available session for call.');
    }
    _validateOutgoingCall(extNum, sipProxy);
    await _holdActiveCalls();
    await _initLocalMedia();
    await sipPluginWrapper.addTrackToPeerConnection(localStream!);
    await sipPluginWrapper.call(displayName: displayName, extNum: extNum, sipProxy: sipProxy);
    return sipPluginWrapper.handleId!;
  }

  Future<void> hangupCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped hangupCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot hangup call. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.hangup();
  }

  Future<void> acceptCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped acceptCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot accept call. No SIP plugin found for handleId $handleId');
    }
    await _holdActiveCalls();
    await _initLocalMedia();
    await sipPluginWrapper.addTrackToPeerConnection(localStream!);
    await sipPluginWrapper.accept();
  }

  Future<void> declineCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped declineCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot decline call. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.decline();
  }

  Future<void> blindTransferCall(int handleId, String extNum, String sipProxy) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped transferCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot blind transfer call. No SIP plugin found for handleId $handleId');
    }
    _validateOutgoingCall(extNum, sipProxy);
    await sipPluginWrapper.transfer(extNum, sipProxy);
  }

  Future<void> initiateAttendedTransfer(int handleId, String displayName, String extNum, sipProxy) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped initiateAttendedTransfer');
      return;
    }
    SipPluginWrapper? transferee = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (transferee == null) {
      throw JanusSipPluginException('Cannot initiate attended transfer. No SIP plugin found for handleId $handleId');
    }
    _validateOutgoingCall(extNum, sipProxy);
    int transferTargetHandleId = await makeCall(displayName, extNum, sipProxy);
    SipPluginWrapper? transferTarget =
        _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == transferTargetHandleId);
    if (transferTarget == null) {
      throw JanusSipPluginException(
          'Cannot initiate attended transfer. No SIP plugin found for handleId $transferTargetHandleId');
    }
    SipTransferModel transferModel = SipTransferModel(
      transfereeUri: transferee.currSipCallState.counterPartUri,
      transfereeName: transferee.currSipCallState.counterPartName,
      transfereeHandleId: transferee.handleId!,
      transferTargetUri: SipPluginWrapper.buildSipUri(extNum, sipProxy),
      transferTargetName: displayName,
      transferTargetHandleId: transferTarget.handleId!,
    );
    transferee.recordTransferData(transferModel);
    transferTarget.recordTransferData(transferModel);
  }

  Future<void> completeAttendedTransfer(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped completeAttendedTransfer');
      return;
    }
    SipPluginWrapper? transferTarget = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (transferTarget == null) {
      throw JanusSipPluginException('Cannot complete attended transfer. No SIP plugin found for handleId $handleId');
    }
    final transferData = transferTarget.currSipCallState.transferData;
    if (transferData == null) {
      throw JanusSipPluginException('Cannot complete attended transfer. No transfer data found for handleId $handleId');
    }
    if (transferTarget.currSipCallState.status.value < SipPluginStatus.accepted.value) {
      throw const JanusSipPluginException('Cannot complete attended transfer. Transfer target has yet to accept call');
    }
    SipPluginWrapper? transferee =
        _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == transferData.transfereeHandleId);
    if (transferee == null) {
      throw const JanusSipPluginException('Cannot complete attended transfer. No SIP plugin found for transferee');
    }
    final (extNum, sipProxy) = SipPluginWrapper.parseSipUri(transferData.transferTargetUri);
    await transferee.transfer(extNum, sipProxy, replaceCallId: transferTarget.currSipCallState.callId);
  }

  Future<void> cancelAttendedTransfer(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped cancelAttendedTransfer');
      return;
    }

    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot cancel attended transfer. No SIP plugin found for handleId $handleId');
    }
    SipTransferModel? transferData = sipPluginWrapper.currSipCallState.transferData;
    if (transferData == null) {
      throw JanusSipPluginException('Cannot cancel attended transfer. No transfer data found for handleId $handleId');
    }
    int otherHandleId = transferData.transfereeHandleId == handleId
        ? transferData.transferTargetHandleId
        : transferData.transfereeHandleId;
    SipPluginWrapper? otherSipPluginWrapper =
        _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == otherHandleId);
    if (otherSipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot cancel attended transfer. No SIP plugin found for handleId $otherHandleId');
    }
    sipPluginWrapper.clearTransferData();
    otherSipPluginWrapper.clearTransferData();
  }

  Future<void> holdCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped holdCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot hold call. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.hold();
  }

  Future<void> unholdCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped unholdCall');
      return;
    }
    await _holdActiveCalls();
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot unhold call. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.unhold();
  }

  Future<void> toggleHoldCall(int handleId) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped toggleHoldCall');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot toggle hold call. No SIP plugin found for handleId $handleId');
    }
    if (sipPluginWrapper.currSipCallState.onhold) {
      await _holdActiveCalls();
    }
    await sipPluginWrapper.toggleHold();
  }

  Future<void> _holdActiveCalls() async {
    Iterable<SipPluginWrapper>? activeCallsNotOnHold = _sipPluginWrappers.values
        .where((w) => w.currSipCallState.status == SipPluginStatus.accepted && !w.currSipCallState.onhold);
    if (activeCallsNotOnHold.isNotEmpty) {
      await Future.wait(activeCallsNotOnHold.map((w) => w.hold()));
    }
  }

  Future<void> sendDTMF(int handleId, String tone) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped sendDTMF');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot send DTMF. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.sendDTMF(tone);
  }

  Future<void> muteMicAudio(int handleId, bool mute) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped muteMicAudio');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot mute mic. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.muteMicAudio(mute);
  }

  Future<void> muteSpeakerAudio(int handleId, bool mute) async {
    if (_isReinitRunning) {
      log.t('reinit is running. Skipped muteSpeakerAudio');
      return;
    }
    SipPluginWrapper? sipPluginWrapper = _sipPluginWrappers.values.firstWhereOrNull((w) => w.handleId == handleId);
    if (sipPluginWrapper == null) {
      throw JanusSipPluginException('Cannot mute speaker. No SIP plugin found for handleId $handleId');
    }
    await sipPluginWrapper.muteSpeakerAudio(mute);
  }

  void _validateOutgoingCall(String extNum, String sipProxy) {
    if (extNum.isEmpty) {
      throw const JanusSipPluginException('Cannot initiate call. extNum is empty');
    }
    if (sipProxy.isEmpty) {
      throw const JanusSipPluginException('Cannot initiate call. sipProxy is empty');
    }
    final sipUri = SipPluginWrapper.buildSipUri(extNum, sipProxy);
    // if (_sipPluginWrappers.values.any((w) => w.currSipCallState.counterPartUri == sipUri)) {
    //   throw JanusSipPluginException('Cannot initiate call. Already have a call with $sipUri');
    // }
    final (selfExt, _) = SipPluginWrapper.parseSipUri(_username!);
    final selfSipUri = SipPluginWrapper.buildSipUri(selfExt, _proxy!);
    if (sipUri == selfSipUri) {
      throw const JanusSipPluginException('Cannot initiate call. Cannot call yourself');
    }
  }
}
