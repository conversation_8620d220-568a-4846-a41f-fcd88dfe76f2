import 'dart:convert';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

/// Service to manage audio codec preferences
class CodecSettingsService with PrefsAware {
  static const List<String> _fallbackAvailableCodecs = [
    'opus',
    'PCMA',
    'PCMU',
    'G722',
    'G729',
    'GSM',
    'telephone-event'
  ];

  List<String>? _cachedAvailableCodecs;

  // Private constructor to prevent the use of constructor
  CodecSettingsService._();

  /// Asynchronous factory constructor to initialize the class.
  static Future<CodecSettingsService> create() async {
    final service = CodecSettingsService._();
    await service.updateAvailableCodecsFromCapabilities();
    return service;
  }

  /// Get the list of available codecs that can be configured
  List<String> get availableCodecs => _cachedAvailableCodecs ?? List.from(_fallbackAvailableCodecs);

  /// default codecs based on server capability
  List<String> get _defaultCodecs {
    final List<String> serverCodecNames = [];
    for (final codec in env!.serverCodec.split(',')) {
      serverCodecNames.add(codec.trim());
    }
    return serverCodecNames;
  }

  /// Update available codecs from WebRTC capabilities
  Future<void> updateAvailableCodecsFromCapabilities() async {
    try {
      final receiverCapabilities = await getRtpReceiverCapabilities('audio');
      final Set<String> receiverCodecNames = {};
      if (receiverCapabilities.codecs != null) {
        for (final codec in receiverCapabilities.codecs!) {
          if (codec.mimeType.startsWith('audio/')) {
            final codecName = codec.mimeType.substring(6); // Remove 'audio/' prefix
            receiverCodecNames.add(codecName);
          }
        }
      }
      log.t('receiver codec capabilities: $receiverCodecNames');

      final senderCapabilities = await getRtpSenderCapabilities('audio');
      final Set<String> senderCodecNames = {};
      if (senderCapabilities.codecs != null) {
        for (final codec in senderCapabilities.codecs!) {
          if (codec.mimeType.startsWith('audio/')) {
            final codecName = codec.mimeType.substring(6); // Remove 'audio/' prefix
            senderCodecNames.add(codecName);
          }
        }
      }
      log.t('sender codec capabilities: $senderCodecNames');

      final Set<String> commonCodecNames = receiverCodecNames.intersection(senderCodecNames);
      final List<String> availableCodecNames = commonCodecNames.toList();
      _cachedAvailableCodecs = availableCodecNames;
      log.t('Updated available codecs from capabilities: $availableCodecNames');
    } catch (e) {
      log.e('Failed to get audio capabilities, using fallback codecs', error: e);
      _cachedAvailableCodecs = null;
    }
  }

  /// Get the current user-configured codec list, or default if not set
  List<String> getConfiguredCodecs() {
    try {
      final codecsJson = prefs.getString(CacheKeys.audioCodecs);
      if (codecsJson != null) {
        final List<dynamic> codecsList = jsonDecode(codecsJson);
        final List<String> sanitized = <String>[];
        for (final codec in codecsList) {
          if (codec is String && availableCodecs.contains(codec) && !sanitized.contains(codec)) {
            sanitized.add(codec);
          }
        }
        if (sanitized.isNotEmpty) {
          return sanitized;
        }
      }
    } catch (e) {
      log.e('Failed to load configured codecs', error: e);
    }
    return List.from(_defaultCodecs);
  }

  /// Save the user-configured codec list
  Future<bool> saveConfiguredCodecs(List<String> codecs) async {
    try {
      final List<String> sanitized = <String>[];
      for (final codec in codecs) {
        if (availableCodecs.contains(codec) && !sanitized.contains(codec)) {
          sanitized.add(codec);
        }
      }
      if (!validateCodecList(sanitized)) {
        log.e('Failed to save configured codecs: invalid list $codecs');
        return false;
      }
      final codecsJson = jsonEncode(sanitized);
      final success = await prefs.setString(CacheKeys.audioCodecs, codecsJson);
      if (success) {
        log.t('Saved configured codecs: $codecs');
      }
      return success;
    } catch (e) {
      log.e('Failed to save configured codecs', error: e);
      return false;
    }
  }

  /// Get codecs that are available but not currently configured
  List<String> getAvailableCodecs(List<String> configuredCodecs) {
    return availableCodecs.where((codec) => !configuredCodecs.contains(codec)).toList();
  }

  /// Reset codecs to default configuration
  Future<bool> resetToDefaults() async {
    return await saveConfiguredCodecs(List.from(_defaultCodecs));
  }

  /// Validate that the codec list contains at least one codec
  bool validateCodecList(List<String> codecs) {
    return codecs.isNotEmpty && codecs.any((codec) => codec != 'telephone-event');
  }
}
