import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/screens/codec_settings.dart';

class SettingsScreen extends StatefulWidget {
  static const routeName = '/settings';

  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: colorTheme.backgroundColor,
            title: const Text('Settings'),
            centerTitle: true,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: colorTheme.primaryColor),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          backgroundColor: colorTheme.backgroundColor,
          body: ListView(
            padding: const EdgeInsets.all(spacingMedium),
            children: [
              _buildSectionHeader('ADVANCED SETTINGS', textTheme, colorTheme),
              const SizedBox(height: spacingSmall),
              _buildSettingsCard(colorTheme, [
                _buildSettingsTile(
                  icon: Icons.graphic_eq,
                  title: 'Audio codecs',
                  onTap: () {
                    pushNamed(CodecSettingsScreen.routeName);
                  },
                  colorTheme: colorTheme,
                  textTheme: textTheme,
                ),
                // _buildDivider(colorTheme),
                // _buildSettingsTile(
                //   icon: Icons.music_note,
                //   title: 'Ringtone',
                //   subtitle: 'marimba',
                //   onTap: () {
                //     // TODO: Implement ringtone settings
                //     log.i('Ringtone settings not implemented yet');
                //   },
                //   colorTheme: colorTheme,
                //   textTheme: textTheme,
                //   enabled: false,
                // ),
              ]),
              // const SizedBox(height: spacingLarge),
              // _buildSectionHeader('ISSUE REPORTING', textTheme, colorTheme),
              // const SizedBox(height: spacingSmall),
              // _buildSettingsCard(colorTheme, [
              //   _buildSettingsTile(
              //     icon: Icons.bug_report,
              //     title: 'Report an issue',
              //     onTap: () {
              //       // TODO: Implement instant messaging settings
              //       log.i('Instant messaging settings not implemented yet');
              //     },
              //     colorTheme: colorTheme,
              //     textTheme: textTheme,
              //     enabled: false,
              //   ),
              // ]),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, TextTheme textTheme, dynamic colorTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: spacingSmall),
      child: Text(
        title,
        style: textTheme.bodySmall?.copyWith(
          color: colorTheme.onBackgroundColor.withOpacity(0.6),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(dynamic colorTheme, List<Widget> children) {
    return Card(
      color: colorTheme.surfaceColor,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusMedium),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    required dynamic colorTheme,
    required TextTheme textTheme,
    bool enabled = true,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: enabled ? colorTheme.primaryColor : colorTheme.onBackgroundColor.withOpacity(0.4),
      ),
      title: Text(
        title,
        style: textTheme.bodyMedium?.copyWith(
          color: enabled ? colorTheme.onBackgroundColor : colorTheme.onBackgroundColor.withOpacity(0.4),
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: textTheme.bodySmall?.copyWith(
                color: enabled
                    ? colorTheme.onBackgroundColor.withOpacity(0.7)
                    : colorTheme.onBackgroundColor.withOpacity(0.4),
              ),
            )
          : null,
      trailing: Icon(
        Icons.chevron_right,
        color: enabled ? colorTheme.onBackgroundColor.withOpacity(0.6) : colorTheme.onBackgroundColor.withOpacity(0.4),
      ),
      onTap: enabled ? onTap : null,
    );
  }

  Widget _buildDivider(dynamic colorTheme) {
    return Divider(
      height: 1,
      color: colorTheme.onBackgroundColor.withOpacity(0.1),
      indent: spacingXXLarge,
    );
  }
}
