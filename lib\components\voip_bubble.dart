import 'package:collection/collection.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Configuration for bubble dimensions and behavior
const double kBubbleWidth = 200.0;
const double kBubbleHeight = 80.0;

class VoipBubbleColors {
  final Color backgroundColor;
  final Color borderColor;
  final Color iconBackgroundColor;
  final Color iconColor;
  final Color textColor;
  final Color subtextColor;

  const VoipBubbleColors({
    required this.backgroundColor,
    required this.borderColor,
    required this.iconBackgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.subtextColor,
  });
}

class VoipBubble extends StatefulWidget {
  final int handleId;
  final VoidCallback onTap;
  final Offset initialPosition;

  const VoipBubble({
    super.key,
    required this.handleId,
    required this.onTap,
    required this.initialPosition,
  });

  @override
  State<VoipBubble> createState() => VoipBubbleState();
}

class VoipBubbleState extends State<VoipBubble> {
  late Offset position;
  bool _isDragging = false;
  bool _isSelected = false;

  // Configuration for bubble dimensions and behavior
  final double bubbleWidth = kBubbleWidth;
  final double bubbleHeight = kBubbleHeight;

  // The default padding from the edge when snapping.
  final double snapPadding = 20.0;

  // The minimum part of the bubble that must remain visible.
  // This prevents the user from losing the bubble completely.
  final double minVisiblePart = 60.0;

  @override
  void initState() {
    super.initState();
    // Initialize the public position property from the widget.
    position = widget.initialPosition;
  }

  void updateSelection(bool isSelected) {
    // Check if the widget is still mounted before calling setState
    if (mounted && _isSelected != isSelected) {
      setState(() {
        _isSelected = isSelected;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<VoipCubit, VoipState, SipCallState?>(
      selector: (state) {
        if (state is VoipRunning) {
          return state.activeCalls.firstWhereOrNull((s) => s.handleId == widget.handleId);
        }
        return null;
      },
      builder: (context, sipCallState) {
        if (sipCallState == null) {
          return const SizedBox.shrink();
        }
        return BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, themeState) {
            final textTheme = themeState.themeData.textTheme;
            final screenWidth = MediaQuery.of(context).size.width;
            final screenHeight = MediaQuery.of(context).size.height;

            final bubbleColors = _getVoipBubbleColors(themeState, sipCallState);
            final bubbleIcon = _getVoipBubbleIcon(sipCallState);
            final bubbleStatusText = _getVoipBubblesStatusText(sipCallState);
            String bubbleDisplayText = 'Unknown';
            if (sipCallState.counterPartName.isNotEmpty) {
              bubbleDisplayText = sipCallState.counterPartName;
            } else if (sipCallState.counterPartUri.isNotEmpty) {
              bubbleDisplayText = sipCallState.counterPartUri;
            }

            return Positioned(
              left: position.dx,
              top: position.dy,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 200),
                opacity: _isSelected ? 1.0 : 0.6,
                child: GestureDetector(
                  onPanStart: (details) {
                    setState(() {
                      _isDragging = true;
                    });
                  },
                  onPanUpdate: (details) {
                    final newPosition = position + details.delta;
                    setState(() {
                      position = Offset(
                        // Allow dragging between the defined off-screen limits.
                        newPosition.dx.clamp(
                          -bubbleWidth + minVisiblePart,
                          screenWidth - minVisiblePart,
                        ),
                        // Keep the bubble within the vertical screen bounds.
                        newPosition.dy.clamp(0.0, screenHeight - bubbleHeight),
                      );
                    });
                  },
                  onPanEnd: (details) {
                    setState(() {
                      _isDragging = false;
                    });
                    _snapToEdge();
                  },
                  onTap: () {
                    if (!_isDragging) {
                      widget.onTap();
                    }
                  },
                  child: Material(
                    elevation: _isDragging ? 8.0 : 4.0,
                    borderRadius: BorderRadius.circular(40),
                    color: bubbleColors.backgroundColor,
                    child: Container(
                      width: bubbleWidth,
                      height: bubbleHeight,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40),
                        border: Border.all(
                          color: bubbleColors.borderColor,
                          width: 2,
                        ),
                      ),
                      child: Row(
                        children: [
                          // State Icon
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: bubbleColors.iconBackgroundColor,
                            ),
                            child: Icon(
                              bubbleIcon,
                              color: bubbleColors.iconColor,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 12),
                          // Call info
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  bubbleDisplayText,
                                  style: textTheme.bodyMedium!.copyWith(
                                    color: bubbleColors.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  bubbleStatusText,
                                  style: textTheme.bodySmall!.copyWith(
                                    color: bubbleColors.subtextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _snapToEdge() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Define the boundaries for the "middle" area of the screen.
    // If the bubble is released here, it will snap.
    final double leftSnapBoundary = snapPadding;
    final double rightSnapBoundary = screenWidth - bubbleWidth - snapPadding;

    // Check if the bubble was released in the middle.
    if (position.dx > leftSnapBoundary && position.dx < rightSnapBoundary) {
      // Determine which edge is closer based on the center of the screen.
      if ((position.dx + bubbleWidth / 2) < screenWidth / 2) {
        // Snap to the default left position.
        setState(() {
          position = Offset(snapPadding, position.dy);
        });
      } else {
        // Snap to the default right position.
        setState(() {
          position = Offset(rightSnapBoundary, position.dy);
        });
      }
    }
    // If the bubble is NOT in the middle area, it means the user has
    // already placed it near an edge (either fully visible or partially hidden).
    // In this case, we do nothing and let it stay where the user dropped it.
  }

  IconData _getVoipBubbleIcon(SipCallState sipCallState) {
    switch (sipCallState.status) {
      case SipPluginStatus.callInitiated:
      case SipPluginStatus.calling:
      case SipPluginStatus.ringing:
      case SipPluginStatus.connecting:
      case SipPluginStatus.proceeding:
        return Icons.phone_in_talk;
      case SipPluginStatus.incomingCall:
        return Icons.phone_callback;
      case SipPluginStatus.accepted:
      case SipPluginStatus.progress:
      case SipPluginStatus.transfer:
        {
          if (sipCallState.onhold) {
            return Icons.phone_paused;
          } else {
            return Icons.call;
          }
        }
      case SipPluginStatus.hangingup:
      case SipPluginStatus.hungup:
        return Icons.call_end;
      default:
        return Icons.call_outlined;
    }
  }

  VoipBubbleColors _getVoipBubbleColors(ThemeState themeState, SipCallState sipCallState) {
    // themeState is for future use, when we want to really change the color based on theme.
    // For now, just use hardcode all the color.
    switch (sipCallState.status) {
      case SipPluginStatus.callInitiated:
      case SipPluginStatus.incomingCall:
      case SipPluginStatus.accepted:
      case SipPluginStatus.progress:
      case SipPluginStatus.calling:
      case SipPluginStatus.proceeding:
      case SipPluginStatus.connecting:
      case SipPluginStatus.ringing:
      case SipPluginStatus.transfer:
        {
          if (sipCallState.onhold) {
            return const VoipBubbleColors(
              backgroundColor: Color(0xFF222222),
              borderColor: Color(0xFFCC8C00),
              iconBackgroundColor: Color(0xFF424242),
              iconColor: Color(0xFFCC8C00),
              textColor: Color(0xFFD1D0CF),
              subtextColor: Color(0xFFCC8C00),
            );
          } else {
            return const VoipBubbleColors(
              backgroundColor: Color(0xFF222222),
              borderColor: Color(0xFF4CAA50),
              iconBackgroundColor: Color(0xFF424242),
              iconColor: Color(0xFF4CAA50),
              textColor: Color(0xFFD1D0CF),
              subtextColor: Color(0xFF4CAA50),
            );
          }
        }
      case SipPluginStatus.hangingup:
      case SipPluginStatus.hungup:
        {
          return const VoipBubbleColors(
            backgroundColor: Color(0xFF222222),
            borderColor: Color(0xFFE05343),
            iconBackgroundColor: Color(0xFF424242),
            iconColor: Color(0xFFE05343),
            textColor: Color(0xFFD1D0CF),
            subtextColor: Color(0xFFE05343),
          );
        }
      default:
        {
          log.w('VoipBubble - Unknown SipCallState: $sipCallState');
          return const VoipBubbleColors(
            backgroundColor: Color(0xFF222222),
            borderColor: Colors.black,
            iconBackgroundColor: Color(0xFF424242),
            iconColor: Colors.black,
            textColor: Color(0xFFD1D0CF),
            subtextColor: Colors.black,
          );
        }
    }
  }

  String _getVoipBubblesStatusText(SipCallState sipCallState) {
    if (sipCallState.onhold) {
      return 'On Hold';
    }
    return sipCallState.status.display;
  }
}
