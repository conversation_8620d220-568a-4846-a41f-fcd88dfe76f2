import 'package:flutter/material.dart';

class VoipCallDialogController extends ChangeNotifier {
  bool _isHoldDialogOpen = false;
  bool get isHoldDialogOpen => _isHoldDialogOpen;
  set isHoldDialogOpen(bool value) {
    if (_isHoldDialogOpen == value) return;
    _isHoldDialogOpen = value;
    notifyListeners();
  }

  bool _isTransferDialogVisible = false;
  bool get isTransferDialogVisible => _isTransferDialogVisible;
  set isTransferDialogVisible(bool value) {
    if (_isTransferDialogVisible == value) return;
    _isTransferDialogVisible = value;
    notifyListeners();
  }
}
