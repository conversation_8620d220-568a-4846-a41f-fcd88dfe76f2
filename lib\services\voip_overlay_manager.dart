import 'package:ddone/components/voip_bubble.dart';
import 'package:ddone/components/voip_call_dialog.dart';
import 'package:ddone/components/voip_call_dialog_controller.dart';
import 'package:ddone/constants/keys/widget_keys.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';

class VoipOverlayManager {
  final Map<int, (OverlayEntry, GlobalKey<VoipBubbleState>)> _overlaied = {};
  final Map<int, VoipCallDialogController> _callDialogControllers = {};
  int? _currentDialogHandleId;
  BuildContext? _currentDialogContext;

  // Constants for layout calculation.
  static const double _bubbleVerticalPadding = 16.0;
  static const double _initialX = 20.0; // from the right edge
  static const double _initialY = 100.0; // from the top edge

  void onVoipStateChanged(BuildContext context, VoipState voipState) {
    final allKeys = _overlaied.keys.toList();
    if (voipState is VoipRunning) {
      // remove floating call bubble for ended call
      final activeCallsHandleIds = voipState.activeCalls.map((c) => c.handleId).toSet();
      for (final handleId in allKeys) {
        if (!activeCallsHandleIds.contains(handleId)) {
          _removeVoipOverlay(handleId);
        }
      }
      // ensure there is a floating call bubble for each of the call
      for (SipCallState activeCall in voipState.activeCalls) {
        _buildVoipOverlay(context, activeCall);
      }
    } else {
      // remove all floating call bubble
      // - call dialog will remove itself when call ended, no need to extra pop().
      for (final handleId in allKeys) {
        _removeVoipOverlay(handleId);
      }
    }
  }

  /// Sets the currently selected bubble and triggers the necessary rebuilds.
  void _setSelection(int? newHandleId) {
    final oldHandleId = _currentDialogHandleId;
    if (oldHandleId == newHandleId) return;

    _currentDialogHandleId = newHandleId;

    _overlaied.forEach((handleId, data) {
      data.$2.currentState?.updateSelection(handleId == newHandleId);
    });
  }

  /// Finds an available on-screen position that doesn't overlap with existing bubbles.
  Offset _findAvailablePosition(BuildContext context) {
    // Get the rectangles of all currently displayed bubbles.
    final existingRects = _overlaied.values
        .map((data) {
          final state = data.$2.currentState;
          if (state == null) return null;
          return Rect.fromLTWH(
            state.position.dx,
            state.position.dy,
            state.bubbleWidth,
            state.bubbleHeight,
          );
        })
        .whereType<Rect>()
        .toList();
    // Start searching for a position.
    final screenWidth = MediaQuery.of(context).size.width;
    final double initialXOnRight = screenWidth - kBubbleWidth - _initialX;
    Offset candidatePosition = Offset(initialXOnRight, _initialY);
    int attempts = 0;
    const maxAttempts = 50; // Safety break to prevent infinite loops.
    while (attempts < maxAttempts) {
      final candidateRect = Rect.fromLTWH(
        candidatePosition.dx,
        candidatePosition.dy,
        kBubbleWidth,
        kBubbleHeight,
      );
      // Check if the candidate position overlaps with any existing bubble.
      // We use a slightly larger rect for the check to ensure padding.
      final collision = existingRects.any((rect) {
        return candidateRect.overlaps(rect.inflate(_bubbleVerticalPadding));
      });
      if (!collision) {
        // If no collision, we've found a valid spot.
        return candidatePosition;
      }
      // If it collides, try the next position down.
      candidatePosition = Offset(
        candidatePosition.dx,
        candidatePosition.dy + _bubbleVerticalPadding,
      );
      // If we go off-screen, we would ideally reset to the other side, but for simplicity, we'll just clamp it for now.
      final screenHeight = MediaQuery.of(context).size.height;
      if (candidatePosition.dy + kBubbleHeight > screenHeight) {
        // Fallback if the screen is full, just place it at the bottom.
        return Offset(_initialX, screenHeight - kBubbleHeight - _bubbleVerticalPadding);
      }
      attempts++;
    }
    // Fallback if we can't find a position after many tries.
    return const Offset(_initialX, _initialY);
  }

  void _buildVoipOverlay(BuildContext context, SipCallState sipCallState) {
    // Avoid showing it if the same overlay already exists.
    if (_overlaied.containsKey(sipCallState.handleId)) {
      log.t('Overlay already exists for handleId:${sipCallState.handleId}. Skipping.');
      return;
    }

    log.t('Building overlay for handleId:${sipCallState.handleId}');

    final initialPosition = _findAvailablePosition(context);
    final bubbleKey = GlobalKey<VoipBubbleState>();

    // Create the overlay entry
    final overlayEntry = OverlayEntry(
      builder: (overlayEntryContext) => VoipBubble(
        key: bubbleKey,
        handleId: sipCallState.handleId,
        onTap: () => _showCallDialog(sipCallState.handleId),
        initialPosition: initialPosition,
      ),
    );

    // Insert the entry into the overlay
    Overlay.of(context).insert(overlayEntry);
    _overlaied[sipCallState.handleId] = (overlayEntry, bubbleKey);

    // Flip to the new call
    Future.delayed(const Duration(milliseconds: 300)).then((_) {
      _showCallDialog(sipCallState.handleId);
    });
  }

  void _removeVoipOverlay(int handleId) {
    if (_overlaied.containsKey(handleId)) {
      log.t('Removing overlay for handleId:$handleId');
      _overlaied[handleId]!.$1.remove();
      _overlaied.remove(handleId);
    }
  }

  void _showCallDialog(int handleId) {
    final context = WidgetKeys.navKey.currentContext;
    if (context == null) {
      log.e('Context is null. Cannot show call dialog.');
      return;
    }
    if (_currentDialogHandleId == handleId) {
      log.t('Call dialog already showing for handleId:$_currentDialogHandleId. Skipping.');
      return;
    }
    log.t('Showing call dialog for handleId:$handleId');
    if (_currentDialogContext != null && Navigator.of(_currentDialogContext!).canPop()) {
      _removeCurrentCallDialog();
    }
    _setSelection(handleId);
    _callDialogControllers[handleId] = VoipCallDialogController();
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        _currentDialogHandleId = handleId;
        _currentDialogContext = dialogContext;
        return VoipCallDialog(
          handleId: handleId,
          controller: _callDialogControllers[handleId]!,
        );
      },
    ).then((_) {
      if (_currentDialogHandleId == handleId) {
        _setSelection(null);
        _currentDialogContext = null;
      }
      _callDialogControllers.remove(handleId);
    });
  }

  void _removeCurrentCallDialog() {
    if (_currentDialogContext != null) {
      final controller = _callDialogControllers[_currentDialogHandleId!];
      if (controller?.isTransferDialogVisible ?? false) {
        pop();
      }
      if (controller?.isHoldDialogOpen ?? false) {
        pop();
      }
      pop();
    }
  }
}
