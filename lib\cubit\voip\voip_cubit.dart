import 'dart:async';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/voip_constants.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/events/voip_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/models/sip_call_state.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/janus/janus_service_exception.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

part 'voip_state.dart';

class VoipCubit extends Cubit<VoipState> with PrefsAware {
  late final VoipService _voipService;
  late final IEventBus _eventBus;

  StreamSubscription<NetworkEvent>? _networkEventSubscription;

  bool _wakeLockEnabled = false;

  VoipCubit._({
    VoipState? state,
  })  : _voipService = sl.get<VoipService>(),
        _eventBus = sl.get<IEventBus>(),
        super(state ?? const VoipInitial()) {
    _networkEventSubscription = _eventBus.on<NetworkEvent>().listen((event) {
      if (event.state is NetworkReconnected) {
        log.t('VoipCubit - eventBus: networkEvent:$event');
        _voipService.maintainConnection();
      } else if (event.state is NetworkDisconnected) {
        _hangupCallWhenNetworkDisconnect();
      }
    });
  }

  factory VoipCubit.initial({VoipState? state}) {
    return sl.isRegistered<VoipCubit>() ? sl.get<VoipCubit>() : VoipCubit._(state: state);
  }

  void _onSipCallsStateData(List<SipCallState> sipCallStates) async {
    log.t('voip cubit sip calls state listener data: $sipCallStates');
    if (sipCallStates.any((s) => s.status.isCallActive)) {
      emit(VoipRunning(activeCalls: sipCallStates.where((s) => s.status.isCallActive).toList()));
      _preventScreenOff(true);
    } else if (sipCallStates.any((s) => s.status == SipPluginStatus.registered)) {
      // VoipConnected means it has the ability to make/receive call
      // This means we must have at least 1 session that is registered.
      emit(const VoipConnected());
      _preventScreenOff(false);
    } else if (sipCallStates.any((s) => s.status == SipPluginStatus.initial)) {
      emit(const VoipConnecting());
      _preventScreenOff(false);
    } else {
      emit(const VoipInitial());
      _preventScreenOff(false);
    }
  }

  void _onSipCallsStateError(dynamic error) async {
    log.e('voip cubit sip calls state listener error: $error');
    emit(VoipError(error.toString()));
    _preventScreenOff(false);
  }

  Future<void> init({
    String? sipWsUrl,
    String? sipNumber,
    String? sipDomain,
    String? sipProxy,
    String? sipSecret,
    String? sipName,
  }) async {
    bool hasInit = await _voipService.init(
      sipWsUrl: sipWsUrl,
      sipNumber: sipNumber,
      sipDomain: sipDomain,
      sipProxy: sipProxy,
      sipSecret: sipSecret,
      sipName: sipName,
    );
    if (!hasInit) return;
    _voipService.prioritySipCallsStateStream
        .addListener(kVoipCubitSipCallsStateStreamPriority, _onSipCallsStateData, onError: _onSipCallsStateError);
  }

  // Future<void> emitVoipEvent(VoipState eventBusState) async {
  //   if (eventBusState is VoipSipIncomingCall) {
  //     // 1. ignore incoming call event if already in call
  //     // 2. when it is hanging up, it may still need to wait for incoming call first then can only decline/hangup call
  //     //    so in such case incoming call is part of the hanging up process.
  //     if (state is VoipSipAccepted || state is VoipSipHangingUp) return;
  //   } else if (eventBusState is VoipSipAccepted) {
  //     _preventScreenOff(true);
  //   } else if (eventBusState is VoipSipHangup) {
  //     _preventScreenOff(false);
  //   } else if (eventBusState is VoipSipRegistered) {
  //     // when ttl between fusion and janus expired, janus will automatically re-register.
  //     // we must NOT overwrite the state when it is in call.
  //     if (state is VoipSipCalling ||
  //         state is VoipSipProceeding ||
  //         state is VoipSipRinging ||
  //         state is VoipSipAccepted ||
  //         state is VoipSipProgress ||
  //         state is VoipSipIncomingCall) {
  //       return;
  //     }
  //   }
  //   emit(eventBusState);
  // }

  Future<void> logout() async {
    await dispose();
    // TODO: temp fix, janus listener wont trigger unregister not sure why
    // - Seems like something wrong with Janus server, janus_client package doesn't receive any response from it.
    //   Checked - Janus server received SIP REGISTER event with expried=0 from fusionPBX.
    //           - Janus server log print our 'successfully unregistered'.
    // emit(const VoipSipUnregistered());
  }

  void _preventScreenOff(bool enabled) {
    if (_wakeLockEnabled == enabled) return;
    try {
      if (isAndroid) {
        WakelockPlus.toggle(enable: enabled);
        _wakeLockEnabled = enabled;
      }
    } catch (e) {
      log.e('failed to toggle wakelock', error: e);
    }
  }

  /// When we accept call from background isolate, it take some to invoke foreground acceptCall method.
  /// This will immediately show the loading screen when user start the app.
  void checkAcceptingCallFromBackground() async {
    // if (prefs.getBool(CacheKeys.acceptedCallFromBackground) ?? false) {
    //   prefs.remove(CacheKeys.acceptedCallFromBackground);
    //   Map<String, String> callInfo = await _voipService.callkitService.getCallerInfo();
    //   emit(VoipSipAcceptedLoading(
    //       statusMessage: VoipSipEvent.loading.statusMessage(),
    //       caller: callInfo['caller'],
    //       callerId: callInfo['callerId'],
    //       callee: callInfo['caller'],
    //       calleeId: callInfo['callerId']));
    // }
  }

  void _hangupCallWhenNetworkDisconnect() async {
    // if (await _voipService.callkitService.hasActiveCalls()) {
    //   EasyLoadingService()
    //       .showErrorWithText('Call ended due to network disconnect.', duration: const Duration(seconds: 7));
    // }
  }

  Future<void> dispose() async {
    try {
      await _voipService.dispose();
    } catch (e) {
      log.e('Failed to dispose VoipCubit', error: e);
    }
  }

  Future<void> maintainConnection() async {
    await _voipService.maintainConnection();
  }

  Future<int> makeCall(String displayName, String extNum, String sipProxy) async {
    int handleId = -1;
    try {
      handleId = await _voipService.makeCall(displayName, extNum, sipProxy);
    } catch (e) {
      log.e('Failed in makeCall', error: e);
      displayError(e);
    }
    return handleId;
  }

  Future<void> hangupCall(int handleId) async {
    try {
      await _voipService.hangupCall(handleId);
    } catch (e) {
      log.e('Failed in hangupCall', error: e);
      displayError(e);

      // Something wrong, Should not happen. Attempt to reset the whole thing
      await dispose();
      await init();
    }
  }

  Future<void> acceptCall(int handleId) async {
    await _voipService.acceptCall(handleId);
  }

  Future<void> declineCall(int handleId) async {
    await _voipService.declineCall(handleId);
  }

  Future<void> blindTransferCall(int handleId, String extNum, String sipProxy) async {
    try {
      await _voipService.blindTransferCall(handleId, extNum, sipProxy);
    } catch (e) {
      log.e('Failed in blindTransferCall', error: e);
      displayError(e);
    }
  }

  Future<void> initiateAttendedTransfer(int handleId, String displayName, String extNum, sipProxy) async {
    try {
      await _voipService.initiateAttendedTransfer(handleId, displayName, extNum, sipProxy);
    } catch (e) {
      log.e('Failed in initiateAttendedTransfer', error: e);
      displayError(e);
    }
  }

  Future<void> completeAttendedTransfer(int handleId) async {
    try {
      await _voipService.completeAttendedTransfer(handleId);
    } catch (e) {
      log.e('Failed in completeAttendedTransfer', error: e);
      displayError(e);
    }
  }

  Future<void> cancelAttendedTransfer(int handleId) async {
    await _voipService.cancelAttendedTransfer(handleId);
  }

  Future<void> holdCall(int handleId) async {
    await _voipService.holdCall(handleId);
  }

  Future<void> unholdCall(int handleId) async {
    await _voipService.unholdCall(handleId);
  }

  Future<void> toggleHoldCall(int handleId) async {
    await _voipService.toggleHoldCall(handleId);
  }

  Future<void> sendDTMF(int handleId, String tone) async {
    await _voipService.sendDTMF(handleId, tone);
  }

  Future<void> muteMicAudio(int handleId, bool mute) async {
    await _voipService.muteMicAudio(handleId, mute);
  }

  Future<void> muteSpeakerAudio(int handleId, bool mute) async {
    await _voipService.muteSpeakerAudio(handleId, mute);
  }

  @override
  Future<void> close() async {
    await dispose();
    _networkEventSubscription?.cancel();
    super.close();
  }

  void displayError(Object error) {
    if (error is JanusSipPluginException) {
      EasyLoadingService().showErrorWithText(error.message);
    } else if (error is JanusServiceException) {
      EasyLoadingService().showErrorWithText(error.message);
    } else {
      EasyLoadingService().showErrorWithText(error.toString());
    }
  }
}
