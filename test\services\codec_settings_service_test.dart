import 'package:flutter_test/flutter_test.dart';
import 'package:ddone/services/codec_settings_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('CodecSettingsService', () {
    late CodecSettingsService codecSettingsService;

    setUp(() async {
      // Initialize SharedPreferences with empty values
      SharedPreferences.setMockInitialValues({});
      codecSettingsService = await CodecSettingsService.create();
    });

    test('should return default codecs when no configuration is saved', () {
      final configuredCodecs = codecSettingsService.getConfiguredCodecs();
      expect(configuredCodecs, ['opus', 'PCMA', 'PCMU', 'G722', 'telephone-event']);
    });

    test('should return fallback available codecs when capabilities not updated', () {
      final availableCodecs = codecSettingsService.availableCodecs;
      expect(availableCodecs, contains('opus'));
      expect(availableCodecs, contains('PCMA'));
      expect(availableCodecs, contains('PCMU'));
      expect(availableCodecs, contains('G722'));
    });

    test('should save and retrieve configured codecs', () async {
      final testCodecs = ['PCMA', 'opus', 'G722'];

      final success = await codecSettingsService.saveConfiguredCodecs(testCodecs);
      expect(success, true);

      final retrievedCodecs = codecSettingsService.getConfiguredCodecs();
      expect(retrievedCodecs, testCodecs);
    });

    test('should validate codec list correctly', () {
      expect(codecSettingsService.validateCodecList(['opus', 'PCMA']), true);
      expect(codecSettingsService.validateCodecList(['telephone-event']), false);
      expect(codecSettingsService.validateCodecList([]), false);
      expect(codecSettingsService.validateCodecList(['opus', 'telephone-event']), true);
    });

    test('should reset to defaults', () async {
      // First save some custom codecs
      await codecSettingsService.saveConfiguredCodecs(['PCMA', 'PCMU']);
      expect(codecSettingsService.getConfiguredCodecs(), ['PCMA', 'PCMU']);

      // Reset to defaults
      final success = await codecSettingsService.resetToDefaults();
      expect(success, true);

      final defaultCodecs = codecSettingsService.getConfiguredCodecs();
      expect(defaultCodecs, ['opus', 'PCMA', 'PCMU', 'G722', 'telephone-event']);
    });

    test('should get available codecs excluding configured ones', () {
      final configuredCodecs = ['opus', 'PCMA'];
      final availableCodecs = codecSettingsService.getAvailableCodecs(configuredCodecs);

      expect(availableCodecs, isNot(contains('opus')));
      expect(availableCodecs, isNot(contains('PCMA')));
      expect(availableCodecs, contains('PCMU'));
      expect(availableCodecs, contains('G722'));
    });
  });
}
