import 'package:audio_session/audio_session.dart';
import 'package:ddone/services/audio_session_service/audio_session_service.dart';
import 'package:ddone/utils/logger_util.dart';

class AudioSessionServiceDefault implements AudioSessionService {
  // Private constructor
  AudioSessionServiceDefault._();

  // public static async factory method
  static Future<AudioSessionServiceDefault> create() async {
    final service = AudioSessionServiceDefault._();
    await service._initAudioSession();
    return service;
  }

  late final AudioSession audioSession;
  bool _interrupted = false;

  @override
  bool get interrupted => _interrupted;

  Future<void> _initAudioSession() async {
    audioSession = await AudioSession.instance;
    audioSession.interruptionEventStream.listen((event) {
      if (event.begin) {
        _interrupted = true;
        log.t('Audio session interrupted. type:${event.type}');
      } else {
        _interrupted = false;
        log.t('Audio session interruption ended.');
      }
    });
  }

  @override
  Future<void> resetSession() async {
    log.t('Reseting audio session. Current audioSession config=${audioSession.configuration?.toJson()}');
    try {
      bool success = await audioSession.setActive(false); // tell OS that we are no longer using audio.
      if (!success) {
        log.w('Audio session deactivation failed. Proceeding with configuration reset.');
      } else {
        log.t('Audio session deactivated successfully.');
      }
      // reset it back to normal music audio config.
      await audioSession.configure(const AudioSessionConfiguration.music());
    } catch (e) {
      log.w('Failed to reset audio session', error: e);
    }
  }

  @override
  Future<void> activateRingtoneSession() async {
    log.t('Activating audio session for ringtone...');
    await audioSession.configure(const AudioSessionConfiguration(
      // --- iOS Configuration ---
      avAudioSessionCategory: AVAudioSessionCategory.playback,
      avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
      avAudioSessionMode: AVAudioSessionMode.defaultMode,
      avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.notifyOthersOnDeactivation,

      // --- Android Configuration ---
      androidAudioAttributes: AndroidAudioAttributes(
        contentType: AndroidAudioContentType.sonification,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.notificationRingtone,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: false,
    ));
    bool success = await audioSession.setActive(true);
    if (!success) {
      log.w('Audio session activation failed for ringtone!');
    } else {
      log.t('Audio session activated successfully for ringtone.');
    }
  }

  @override
  Future<void> activateCallSession() async {
    try {
      log.t('Activating audio session for call...');
      await audioSession.configure(AudioSessionConfiguration(
        // --- iOS Configuration ---
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.allowBluetooth |
            AVAudioSessionCategoryOptions.allowBluetoothA2dp |
            AVAudioSessionCategoryOptions.allowAirPlay |
            AVAudioSessionCategoryOptions.duckOthers,
        avAudioSessionMode: AVAudioSessionMode.voiceChat,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.notifyOthersOnDeactivation,

        // --- Android Configuration ---
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: true,
      ));
      bool success = await audioSession.setActive(true);
      if (!success) {
        log.w('Audio session activation failed for call!');
      } else {
        log.t('Audio session activated successfully for call.');
      }
    } catch (e) {
      log.e('Failed to activate audio session for call.', error: e);
    }
  }
}
