* `MediaStreamTrack`
  * A single channel of media, either audio (from a microphone) or video (from a camera).

* `MediaStream`
  * A bundle or container for one or more MediaStreamTracks.
  * `localStream`: The stream you create from your own camera and microphone to send to others.
  * `remoteStream`: The stream you receive from someone else.

* `RTCVideoRenderer`
  * A UI widget whose only job is to display the video from a MediaStream on the screen.
  * You don't need this if you are only handling audio or data.

* `RTCPeerConnection`
  * The core engine of WebRTC. It's the actual connection (the "pipe") between you and another peer.
  * It sends your localStream and receives the remoteStream.
  * It handles all the complex networking and security behind the scenes.

* Handling Multiple Calls
  * You need one RTCPeerConnection for each person you connect with.
  * You can efficiently reuse your single localStream and add its tracks to every RTCPeerConnection.
  * Each connection will provide a separate remoteStream from each peer.

* Muting and Unmuting
  * To stop sending your audio or video, you don't remove the track from the connection.
  * Instead, you get the track from your localStream and set its enabled property:
    * myAudioTrack.enabled = false; (Mute)
    * myAudioTrack.enabled = true; (Unmute)
  * This change affects all peer connections that are using that track.